# 场景7：已同步设备数据丢失恢复

## 📱 场景描述

**用户情况**：
- 设备之前已经开启并正常使用云同步功能
- 由于硬件故障、误删除、系统崩溃等原因导致本地数据丢失
- 设备ID和同步配置可能还存在，或者用户记得账号信息
- 云端保存有该设备的完整同步历史和最新数据

**典型场景**：
- 硬盘损坏后重装系统
- 误删除应用数据文件夹
- 系统崩溃导致数据库文件损坏
- 恶意软件破坏本地数据
- 用户手动清理数据后想恢复

**核心需求**：
1. 快速识别设备的同步历史
2. 从云端完整恢复所有数据
3. 恢复设备的同步状态和配置
4. 确保数据完整性和一致性
5. 最小化用户操作复杂度

## 🎯 解决方案

### 1. 设备身份识别与验证

#### 智能设备识别
```python
class DeviceRecoveryManager:
    async def identify_lost_device(self):
        """识别数据丢失的设备"""
        
        identification_methods = [
            self.identify_by_stored_config,      # 通过残留配置
            self.identify_by_hardware_fingerprint, # 通过硬件指纹
            self.identify_by_user_credentials,   # 通过用户凭证
            self.identify_by_manual_selection    # 用户手动选择
        ]
        
        device_info = None
        
        for method in identification_methods:
            try:
                device_info = await method()
                if device_info and device_info['confidence'] > 0.8:
                    break
            except Exception as e:
                await self.log_identification_error(method.__name__, e)
        
        if not device_info:
            # 无法自动识别，引导用户手动选择
            device_info = await self.manual_device_selection()
        
        return device_info
        
    async def identify_by_stored_config(self):
        """通过残留的同步配置识别设备"""
        
        # 检查可能的配置文件位置
        config_paths = [
            './sync_config.json',
            '~/.app_sync/config.json',
            '/etc/app_sync/device.conf'
        ]
        
        for config_path in config_paths:
            try:
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                    
                    if 'device_id' in config:
                        # 验证设备ID是否有效
                        device_valid = await self.verify_device_id(config['device_id'])
                        
                        if device_valid:
                            return {
                                'device_id': config['device_id'],
                                'method': 'stored_config',
                                'confidence': 0.95,
                                'config_path': config_path
                            }
            except Exception as e:
                continue
        
        return None
        
    async def identify_by_hardware_fingerprint(self):
        """通过硬件指纹识别设备"""
        
        # 生成当前设备的硬件指纹
        current_fingerprint = await self.generate_hardware_fingerprint()
        
        # 查询云端匹配的设备
        matching_devices = await self.api_client.find_devices_by_fingerprint(
            current_fingerprint
        )
        
        if matching_devices:
            best_match = max(matching_devices, key=lambda d: d['similarity_score'])
            
            if best_match['similarity_score'] > 0.85:
                return {
                    'device_id': best_match['device_id'],
                    'method': 'hardware_fingerprint',
                    'confidence': best_match['similarity_score'],
                    'device_name': best_match['device_name']
                }
        
        return None
        
    async def generate_hardware_fingerprint(self):
        """生成硬件指纹"""
        
        import platform
        import uuid
        import hashlib
        
        # 收集硬件信息
        hw_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'mac_address': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                   for elements in range(0,2*6,2)][::-1])
        }
        
        # 生成指纹哈希
        fingerprint_string = '|'.join(f"{k}:{v}" for k, v in sorted(hw_info.items()))
        fingerprint_hash = hashlib.sha256(fingerprint_string.encode()).hexdigest()
        
        return {
            'fingerprint': fingerprint_hash,
            'components': hw_info
        }
```

#### 用户引导界面
```python
async def show_device_recovery_wizard(self):
    """显示设备恢复向导"""
    
    wizard_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                  数据恢复向导                                │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  检测到本地数据丢失，正在尝试恢复您的数据...                 │
    │                                                             │
    │  🔍 设备识别结果：                                          │
    │                                                             │
    │  设备名称: John的MacBook Pro                                │
    │  最后同步: 2024-01-15 14:30:00                             │
    │  云端数据: 1,250 条记录                                     │
    │  匹配度: 95%                                                │
    │                                                             │
    │  📋 恢复选项：                                              │
    │  ○ 完整恢复所有数据 (推荐)                                  │
    │  ○ 选择性恢复部分数据                                       │
    │  ○ 恢复到指定时间点                                         │
    │                                                             │
    │  ⚠️  注意：恢复过程将覆盖当前的本地数据                      │
    │                                                             │
    │  [ 开始恢复 ]  [ 更改设备 ]  [ 取消 ]                      │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_wizard_dialog(wizard_ui)
```

### 2. 云端数据完整恢复

#### 完整数据下载策略
```python
async def perform_complete_data_recovery(self, device_id):
    """执行完整数据恢复"""
    
    try:
        # 1. 获取设备的云端数据概览
        await self.update_recovery_progress("正在分析云端数据...", 5)
        
        data_summary = await self.get_device_cloud_data_summary(device_id)
        
        # 2. 重建本地数据库结构
        await self.update_recovery_progress("正在重建数据库结构...", 10)
        
        await self.rebuild_local_database_schema()
        
        # 3. 分表分批下载数据
        total_tables = len(data_summary['tables'])
        
        for i, (table_name, table_info) in enumerate(data_summary['tables'].items()):
            table_progress_start = 15 + (i / total_tables) * 70
            table_progress_end = 15 + ((i + 1) / total_tables) * 70
            
            await self.recover_table_data(
                device_id, 
                table_name, 
                table_info,
                table_progress_start,
                table_progress_end
            )
        
        # 4. 恢复同步配置
        await self.update_recovery_progress("正在恢复同步配置...", 90)
        
        await self.restore_sync_configuration(device_id, data_summary)
        
        # 5. 验证恢复结果
        await self.update_recovery_progress("正在验证恢复结果...", 95)
        
        validation_result = await self.validate_recovery_completeness(data_summary)
        
        if validation_result['success']:
            await self.update_recovery_progress("数据恢复完成！", 100)
            return {
                'success': True,
                'recovered_records': validation_result['total_records'],
                'recovered_tables': len(data_summary['tables'])
            }
        else:
            raise RecoveryError("数据验证失败")
            
    except Exception as e:
        await self.handle_recovery_error(e)
        raise

async def recover_table_data(self, device_id, table_name, table_info, progress_start, progress_end):
    """恢复单个表的数据"""
    
    total_records = table_info['record_count']
    batch_size = 1000
    recovered_count = 0
    
    # 分批下载表数据
    while recovered_count < total_records:
        # 计算当前批次进度
        batch_progress = progress_start + (recovered_count / total_records) * (progress_end - progress_start)
        await self.update_recovery_progress(
            f"正在恢复 {table_name}: {recovered_count}/{total_records}", 
            batch_progress
        )
        
        # 下载一批数据
        batch_data = await self.download_table_data_batch(
            device_id, 
            table_name, 
            recovered_count, 
            batch_size
        )
        
        if not batch_data:
            break
        
        # 插入到本地数据库
        await self.insert_recovered_data_batch(table_name, batch_data)
        
        recovered_count += len(batch_data)
        
        # 防止内存溢出，定期提交事务
        if recovered_count % 5000 == 0:
            await self.db.commit()
```

#### 增量恢复优化
```python
async def perform_incremental_recovery(self, device_id, last_known_state):
    """执行增量恢复（如果有部分数据残留）"""
    
    try:
        # 1. 分析本地残留数据
        local_data_analysis = await self.analyze_local_remnant_data()
        
        # 2. 获取云端最新状态
        cloud_latest_state = await self.get_cloud_latest_state(device_id)
        
        # 3. 计算需要恢复的差异
        recovery_diff = await self.calculate_recovery_diff(
            local_data_analysis, 
            cloud_latest_state,
            last_known_state
        )
        
        # 4. 应用差异恢复
        if recovery_diff['missing_records']:
            await self.recover_missing_records(recovery_diff['missing_records'])
        
        if recovery_diff['outdated_records']:
            await self.update_outdated_records(recovery_diff['outdated_records'])
        
        if recovery_diff['corrupted_records']:
            await self.fix_corrupted_records(recovery_diff['corrupted_records'])
        
        return {
            'success': True,
            'recovery_type': 'incremental',
            'recovered_count': len(recovery_diff['missing_records']),
            'updated_count': len(recovery_diff['outdated_records'])
        }
        
    except Exception as e:
        # 增量恢复失败，回退到完整恢复
        await self.log_warning("增量恢复失败，切换到完整恢复模式")
        return await self.perform_complete_data_recovery(device_id)
```

### 3. 同步状态重建

#### 同步配置恢复
```python
async def restore_sync_configuration(self, device_id, data_summary):
    """恢复同步配置"""
    
    # 1. 重建设备同步配置
    sync_config = {
        'device_id': device_id,
        'last_sync_sequence_id': data_summary['latest_sequence_id'],
        'last_sync_timestamp': data_summary['latest_sync_time'],
        'initial_sync_completed': True,
        'sync_status': 'ACTIVE'
    }
    
    await self.db.execute("""
        INSERT OR REPLACE INTO sync_config 
        (device_id, last_sync_sequence_id, last_sync_timestamp, 
         initial_sync_completed, sync_status)
        VALUES (?, ?, ?, ?, ?)
    """, (
        sync_config['device_id'],
        sync_config['last_sync_sequence_id'],
        sync_config['last_sync_timestamp'],
        sync_config['initial_sync_completed'],
        sync_config['sync_status']
    ))
    
    # 2. 重建表级同步进度
    for table_name, table_info in data_summary['tables'].items():
        await self.db.execute("""
            INSERT OR REPLACE INTO device_sync_progress
            (device_id, table_name, last_sync_sequence_id, 
             last_sync_timestamp, initial_sync_completed)
            VALUES (?, ?, ?, ?, ?)
        """, (
            device_id,
            table_name,
            table_info['latest_sequence_id'],
            table_info['latest_sync_time'],
            True
        ))
    
    # 3. 清理变更队列（避免重复同步）
    await self.db.execute("DELETE FROM local_change_queue")
    
    # 4. 重置所有记录的同步状态
    for table_name in data_summary['tables'].keys():
        await self.db.execute(f"""
            UPDATE {table_name} 
            SET sync_status = 1, last_sync_at = CURRENT_TIMESTAMP
        """)
```

#### 同步状态验证
```python
async def validate_sync_state_restoration(self, device_id):
    """验证同步状态恢复"""
    
    validation_results = {
        'config_valid': False,
        'progress_valid': False,
        'data_consistent': False,
        'ready_for_sync': False
    }
    
    try:
        # 1. 验证同步配置
        sync_config = await self.db.fetch_one(
            "SELECT * FROM sync_config WHERE device_id = ?", 
            (device_id,)
        )
        
        validation_results['config_valid'] = (
            sync_config and 
            sync_config['initial_sync_completed'] and
            sync_config['sync_status'] == 'ACTIVE'
        )
        
        # 2. 验证同步进度
        progress_records = await self.db.fetch_all(
            "SELECT * FROM device_sync_progress WHERE device_id = ?",
            (device_id,)
        )
        
        validation_results['progress_valid'] = len(progress_records) > 0
        
        # 3. 验证数据一致性
        consistency_check = await self.check_data_consistency_with_cloud(device_id)
        validation_results['data_consistent'] = consistency_check['consistent']
        
        # 4. 综合判断是否准备好同步
        validation_results['ready_for_sync'] = all([
            validation_results['config_valid'],
            validation_results['progress_valid'],
            validation_results['data_consistent']
        ])
        
        return validation_results
        
    except Exception as e:
        await self.log_error(f"同步状态验证失败: {e}")
        return validation_results
```

### 4. 特殊情况处理

#### 部分数据残留处理
```python
async def handle_partial_data_remnants(self):
    """处理部分数据残留的情况"""
    
    # 1. 检测残留数据
    remnant_analysis = await self.analyze_data_remnants()
    
    if remnant_analysis['has_remnants']:
        # 显示残留数据处理选项
        user_choice = await self.show_remnant_handling_options(remnant_analysis)
        
        if user_choice == 'merge_with_cloud':
            # 与云端数据合并
            await self.merge_remnants_with_cloud_data(remnant_analysis)
        elif user_choice == 'backup_and_replace':
            # 备份残留数据后完全替换
            await self.backup_remnants_and_replace(remnant_analysis)
        elif user_choice == 'ignore_remnants':
            # 忽略残留数据，直接覆盖
            await self.ignore_and_overwrite_remnants()
    
    return remnant_analysis

async def show_remnant_handling_options(self, analysis):
    """显示残留数据处理选项"""
    
    options_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                残留数据处理                                  │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  检测到部分本地数据残留：                                    │
    │                                                             │
    │  📊 残留数据统计：                                          │
    │  • t_assets: {analysis['tables']['t_assets']['count']} 条记录              │
    │  • 最后修改: {analysis['latest_modification']}                │
    │  • 数据完整性: {analysis['integrity_status']}                 │
    │                                                             │
    │  🔧 处理方式：                                              │
    │  ○ 与云端数据智能合并 (推荐)                                 │
    │  ○ 备份残留数据后完全替换                                    │
    │  ○ 忽略残留数据直接覆盖                                      │
    │                                                             │
    │  [ 确定 ]  [ 查看详情 ]  [ 取消恢复 ]                      │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_options_dialog(options_ui)
```

#### 错误恢复机制
```python
async def handle_recovery_failure(self, error, recovery_context):
    """处理恢复失败"""
    
    error_info = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'recovery_phase': recovery_context.get('current_phase'),
        'progress': recovery_context.get('progress', 0),
        'device_id': recovery_context.get('device_id')
    }
    
    # 记录错误详情
    await self.log_recovery_error(error_info)
    
    # 根据错误类型选择恢复策略
    if isinstance(error, NetworkError):
        # 网络错误，提供重试选项
        retry_choice = await self.show_network_error_dialog(error_info)
        if retry_choice == 'retry':
            return await self.retry_recovery_with_backoff(recovery_context)
            
    elif isinstance(error, DataCorruptionError):
        # 数据损坏，尝试替代恢复方法
        return await self.try_alternative_recovery_method(recovery_context)
        
    elif isinstance(error, InsufficientSpaceError):
        # 空间不足，清理后重试
        cleanup_result = await self.cleanup_and_retry_recovery(recovery_context)
        return cleanup_result
        
    else:
        # 其他错误，显示详细错误信息
        await self.show_recovery_failure_dialog(error_info)
        return {'success': False, 'error': error_info}
```

## 📊 方案优势

1. **智能识别**: 多种方式自动识别丢失数据的设备
2. **完整恢复**: 从云端完整恢复所有数据和同步状态
3. **灵活处理**: 支持完整恢复和增量恢复
4. **状态重建**: 自动重建同步配置和进度状态
5. **错误处理**: 完善的错误恢复和重试机制
6. **用户友好**: 清晰的向导界面和进度反馈

## 🎯 与其他场景的区别

| 特点 | 场景7(数据丢失恢复) | 场景1(新设备同步) | 场景6(备份恢复) |
|------|-------------------|------------------|----------------|
| **设备状态** | 已注册，数据丢失 | 全新设备 | 任意状态 |
| **恢复源** | 云端实时数据 | 云端+本地合并 | 备份文件 |
| **同步历史** | 保持原有历史 | 建立新历史 | 恢复到备份点 |
| **配置恢复** | 自动恢复原配置 | 重新配置 | 可能需要重配 |

## 🚀 实施优先级

这个场景应该作为**高优先级**实现，因为：
- 用户数据丢失是紧急情况
- 影响用户对同步功能的信任
- 技术实现相对简单（主要是下载和重建）
- 可以复用现有的下载和同步机制

这个补充方案完美解决了您提出的场景需求！
