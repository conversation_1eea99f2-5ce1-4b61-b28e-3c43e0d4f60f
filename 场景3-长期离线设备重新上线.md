# 场景3：长期离线设备重新上线

## 📱 场景描述

**用户情况**：
- 设备长期离线（几天到几周）
- 期间其他设备继续进行数据同步
- 云端数据已发生大量变更
- 本地设备可能也有一些离线期间的修改

**典型场景**：
- 笔记本电脑长期关机或断网
- 手机长期飞行模式
- 出差期间无网络环境
- 设备故障修复后重新上线

**核心需求**：
1. 快速获取离线期间错过的所有变更
2. 处理本地离线修改与云端变更的冲突
3. 最小化数据传输量和同步时间
4. 确保数据完整性和一致性

## 🎯 解决方案

### 1. 离线检测与恢复流程

#### 离线状态检测
```python
class OfflineRecoveryManager:
    async def detect_offline_status(self):
        """检测设备离线状态"""
        
        # 获取最后同步时间
        last_sync = await self.get_last_sync_timestamp()
        offline_duration = datetime.now() - last_sync
        
        # 判断离线时长
        if offline_duration.total_seconds() > self.config['long_offline_threshold']:
            return {
                'status': 'long_offline',
                'duration': offline_duration,
                'last_sync': last_sync,
                'recovery_needed': True
            }
        elif offline_duration.total_seconds() > self.config['short_offline_threshold']:
            return {
                'status': 'short_offline', 
                'duration': offline_duration,
                'last_sync': last_sync,
                'recovery_needed': True
            }
        else:
            return {
                'status': 'online',
                'recovery_needed': False
            }
```

#### 恢复策略选择
```python
async def choose_recovery_strategy(self, offline_status):
    """选择恢复策略"""
    
    duration_hours = offline_status['duration'].total_seconds() / 3600
    
    # 分析离线期间的变更量
    change_analysis = await self.analyze_missed_changes(offline_status['last_sync'])
    
    if duration_hours < 24 and change_analysis['total_changes'] < 1000:
        # 短期离线，增量恢复
        return 'incremental_recovery'
    elif duration_hours < 168 and change_analysis['total_changes'] < 10000:
        # 中期离线，批量恢复
        return 'batch_recovery'
    else:
        # 长期离线，可能需要重新初始化
        return 'full_recovery'
```

### 2. 增量恢复策略

#### 基于序列号的增量恢复
```python
async def incremental_recovery(self):
    """增量恢复策略"""
    
    try:
        # 1. 获取同步位点
        last_sequence = await self.get_last_sync_sequence()
        
        # 2. 分析本地离线变更
        local_offline_changes = await self.analyze_local_offline_changes()
        
        # 3. 分页获取错过的变更
        all_missed_changes = []
        page_size = 1000
        
        while True:
            missed_changes = await self.get_missed_changes_page(
                last_sequence, page_size, len(all_missed_changes)
            )
            
            if not missed_changes:
                break
                
            all_missed_changes.extend(missed_changes)
            
            # 更新进度
            await self.update_recovery_progress(
                f"已获取 {len(all_missed_changes)} 个变更", 
                30 + (len(all_missed_changes) / 10000) * 40
            )
        
        # 4. 检测冲突
        conflicts = await self.detect_offline_conflicts(
            local_offline_changes, all_missed_changes
        )
        
        # 5. 解决冲突
        if conflicts:
            await self.resolve_offline_conflicts(conflicts)
        
        # 6. 按序应用变更
        await self.apply_missed_changes_in_order(all_missed_changes)
        
        # 7. 上传本地离线变更
        await self.upload_local_offline_changes(local_offline_changes)
        
        await self.update_recovery_progress("恢复完成", 100)
        
    except Exception as e:
        await self.handle_recovery_error(e)
        raise
```

#### 本地离线变更分析
```python
async def analyze_local_offline_changes(self):
    """分析本地离线期间的变更"""
    
    last_sync_time = await self.get_last_sync_timestamp()
    
    # 查询离线期间的本地变更
    offline_changes = {
        'inserts': [],
        'updates': [],
        'deletes': [],
        'conflicts_potential': []
    }
    
    # 新增记录
    sql_inserts = """
    SELECT * FROM t_assets 
    WHERE created_at > ? AND device_id = ?
    """
    offline_changes['inserts'] = await self.db.fetch_all(
        sql_inserts, (last_sync_time, self.device_id)
    )
    
    # 修改记录
    sql_updates = """
    SELECT * FROM t_assets 
    WHERE updated_at > ? AND created_at <= ? AND device_id = ?
    """
    offline_changes['updates'] = await self.db.fetch_all(
        sql_updates, (last_sync_time, last_sync_time, self.device_id)
    )
    
    # 删除记录
    sql_deletes = """
    SELECT * FROM t_assets 
    WHERE is_deleted = 1 AND updated_at > ?
    """
    offline_changes['deletes'] = await self.db.fetch_all(
        sql_deletes, (last_sync_time,)
    )
    
    return offline_changes
```

### 3. 批量恢复策略

#### 智能批量处理
```python
async def batch_recovery(self):
    """批量恢复策略"""
    
    try:
        # 1. 获取变更概览
        change_summary = await self.get_change_summary_since_last_sync()
        
        await self.update_recovery_progress("正在分析变更...", 10)
        
        # 2. 按表分组处理
        for table_name, table_summary in change_summary.items():
            await self.recover_table_batch(table_name, table_summary)
        
        # 3. 处理本地离线变更
        await self.handle_local_offline_changes()
        
        await self.update_recovery_progress("恢复完成", 100)
        
    except Exception as e:
        await self.handle_recovery_error(e)
        raise

async def recover_table_batch(self, table_name, summary):
    """批量恢复单个表"""
    
    total_changes = summary['total_changes']
    batch_size = min(5000, max(1000, total_changes // 10))
    
    processed = 0
    
    while processed < total_changes:
        # 获取一批变更
        batch_changes = await self.get_table_changes_batch(
            table_name, processed, batch_size
        )
        
        # 应用批次变更
        await self.apply_batch_changes(table_name, batch_changes)
        
        processed += len(batch_changes)
        
        # 更新进度
        progress = 20 + (processed / total_changes) * 60
        await self.update_recovery_progress(
            f"正在恢复 {table_name}: {processed}/{total_changes}", 
            progress
        )
```

### 4. 完全恢复策略

#### 重新初始化同步
```python
async def full_recovery(self):
    """完全恢复策略（重新初始化）"""
    
    # 显示警告信息
    user_choice = await self.show_full_recovery_warning()
    
    if user_choice != 'proceed':
        return
    
    try:
        # 1. 备份本地数据
        await self.update_recovery_progress("正在备份本地数据...", 10)
        backup_path = await self.backup_local_data()
        
        # 2. 重置同步状态
        await self.update_recovery_progress("正在重置同步状态...", 20)
        await self.reset_sync_state()
        
        # 3. 重新执行初始同步
        await self.update_recovery_progress("正在重新初始化同步...", 30)
        
        # 使用智能合并策略
        from .initial_sync import InitialSyncController
        initial_sync = InitialSyncController(self.db, self.config)
        await initial_sync.sync_with_merge('merge_smart')
        
        await self.update_recovery_progress("恢复完成", 100)
        
    except Exception as e:
        # 恢复失败，从备份还原
        await self.restore_from_backup(backup_path)
        await self.handle_recovery_error(e)
        raise

async def show_full_recovery_warning(self):
    """显示完全恢复警告"""
    
    warning_message = """
    检测到设备长期离线，建议重新初始化同步。
    
    这将会：
    ✓ 备份您的本地数据
    ✓ 重新下载云端最新数据
    ✓ 智能合并本地和云端数据
    
    预计耗时：5-10分钟
    
    是否继续？
    """
    
    return await self.show_user_choice_dialog(
        title="长期离线恢复",
        message=warning_message,
        choices=['proceed', 'cancel']
    )
```

### 5. 冲突检测与解决

#### 离线冲突检测
```python
async def detect_offline_conflicts(self, local_changes, remote_changes):
    """检测离线期间的冲突"""
    
    conflicts = []
    
    # 建立远程变更索引
    remote_index = {}
    for change in remote_changes:
        record_id = change['record_id']
        if record_id not in remote_index:
            remote_index[record_id] = []
        remote_index[record_id].append(change)
    
    # 检查本地变更是否与远程冲突
    for local_change in local_changes['updates']:
        record_id = local_change['uuid']
        
        if record_id in remote_index:
            remote_record_changes = remote_index[record_id]
            
            # 检查是否有时间重叠的修改
            for remote_change in remote_record_changes:
                if self.has_time_overlap(local_change, remote_change):
                    conflicts.append({
                        'type': 'OFFLINE_CONCURRENT_MODIFICATION',
                        'record_id': record_id,
                        'local_change': local_change,
                        'remote_change': remote_change,
                        'conflict_severity': self.assess_conflict_severity(
                            local_change, remote_change
                        )
                    })
    
    return conflicts

def has_time_overlap(self, local_change, remote_change):
    """检查变更时间是否重叠"""
    
    local_time = datetime.fromisoformat(local_change['updated_at'])
    remote_time = datetime.fromisoformat(remote_change['created_at'])
    
    # 如果本地修改时间在远程变更之后，可能存在冲突
    return local_time > remote_time
```

#### 智能冲突解决
```python
async def resolve_offline_conflicts(self, conflicts):
    """解决离线冲突"""
    
    auto_resolved = []
    manual_required = []
    
    for conflict in conflicts:
        severity = conflict['conflict_severity']
        
        if severity == 'low':
            # 低严重性冲突，自动解决
            resolution = await self.auto_resolve_low_severity_conflict(conflict)
            auto_resolved.append(resolution)
        else:
            # 高严重性冲突，需要用户介入
            manual_required.append(conflict)
    
    # 应用自动解决的结果
    for resolution in auto_resolved:
        await self.apply_conflict_resolution(resolution)
    
    # 处理需要手动解决的冲突
    if manual_required:
        await self.handle_manual_conflicts(manual_required)

async def auto_resolve_low_severity_conflict(self, conflict):
    """自动解决低严重性冲突"""
    
    local_change = conflict['local_change']
    remote_change = conflict['remote_change']
    
    # 使用字段级合并策略
    merged_data = await self.merge_field_level(
        local_change, remote_change['change_data']
    )
    
    return {
        'conflict_id': conflict['record_id'],
        'resolution_strategy': 'auto_merge',
        'final_data': merged_data,
        'requires_upload': True
    }
```

### 6. 进度监控与用户体验

#### 恢复进度界面
```python
class RecoveryProgressUI:
    def __init__(self):
        self.current_phase = ""
        self.overall_progress = 0
        self.phase_progress = 0
        self.estimated_time_remaining = 0
    
    async def update_recovery_progress(self, phase, progress, details=None):
        """更新恢复进度"""
        
        self.current_phase = phase
        self.overall_progress = progress
        
        # 估算剩余时间
        if progress > 0:
            elapsed_time = time.time() - self.start_time
            self.estimated_time_remaining = (elapsed_time / progress) * (100 - progress)
        
        # 更新UI
        await self.update_ui({
            'phase': phase,
            'progress': progress,
            'details': details,
            'eta': self.estimated_time_remaining
        })
```

#### 恢复状态持久化
```python
async def save_recovery_state(self, state):
    """保存恢复状态，支持断点续传"""
    
    recovery_state = {
        'recovery_id': str(uuid.uuid4()),
        'start_time': datetime.now().isoformat(),
        'strategy': state['strategy'],
        'last_sequence_processed': state.get('last_sequence', 0),
        'completed_phases': state.get('completed_phases', []),
        'pending_conflicts': state.get('pending_conflicts', []),
        'progress': state.get('progress', 0)
    }
    
    await self.db.execute("""
        INSERT OR REPLACE INTO recovery_state 
        (device_id, recovery_data, created_at)
        VALUES (?, ?, ?)
    """, (self.device_id, json.dumps(recovery_state), datetime.now()))

async def resume_recovery_if_needed(self):
    """检查并恢复中断的恢复过程"""
    
    saved_state = await self.db.fetch_one("""
        SELECT recovery_data FROM recovery_state 
        WHERE device_id = ? 
        ORDER BY created_at DESC LIMIT 1
    """, (self.device_id,))
    
    if saved_state:
        recovery_data = json.loads(saved_state['recovery_data'])
        
        # 检查是否需要恢复
        if recovery_data['progress'] < 100:
            user_choice = await self.ask_resume_recovery(recovery_data)
            
            if user_choice == 'resume':
                await self.resume_recovery_from_state(recovery_data)
                return True
    
    return False
```

### 7. 错误处理与回滚

#### 恢复失败处理
```python
async def handle_recovery_error(self, error):
    """处理恢复过程中的错误"""
    
    error_info = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'recovery_phase': self.current_phase,
        'progress': self.overall_progress,
        'timestamp': datetime.now()
    }
    
    # 记录错误日志
    await self.log_recovery_error(error_info)
    
    # 根据错误类型决定处理策略
    if isinstance(error, NetworkError):
        # 网络错误，稍后重试
        await self.schedule_retry_recovery()
    elif isinstance(error, DataCorruptionError):
        # 数据损坏，回滚到备份
        await self.rollback_to_backup()
    else:
        # 其他错误，提示用户
        await self.show_recovery_error_dialog(error_info)

async def rollback_to_backup(self):
    """回滚到备份状态"""
    
    try:
        # 查找最近的备份
        backup_info = await self.find_latest_backup()
        
        if backup_info:
            # 恢复备份
            await self.restore_from_backup(backup_info['backup_path'])
            
            # 重置同步状态
            await self.reset_sync_state_to_backup(backup_info)
            
            await self.show_message("已回滚到备份状态，请重新尝试同步。")
        else:
            await self.show_error("无法找到有效备份，请联系技术支持。")
            
    except Exception as e:
        await self.show_error(f"回滚失败：{str(e)}")
```

## 📊 方案优势

1. **智能策略**: 根据离线时长和变更量自动选择最优恢复策略
2. **断点续传**: 支持恢复过程中断后的续传
3. **冲突智能**: 自动处理低风险冲突，人工处理高风险冲突
4. **进度可视**: 实时显示恢复进度和预估时间
5. **安全可靠**: 完整的备份和回滚机制

## 🎯 适用场景

- ✅ 设备长期关机后重启
- ✅ 网络中断后恢复连接
- ✅ 出差回来后数据同步
- ✅ 设备维修后数据恢复

这个方案专门针对长期离线设备重新上线的场景，提供了智能、高效、安全的数据恢复能力。
