# SQLite数据同步部署配置指南

## 1. 环境准备

### 1.1 系统要求

#### 客户端环境
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Python**: 3.8+
- **SQLite**: 3.35+
- **内存**: 最小2GB，推荐4GB+
- **存储**: 至少1GB可用空间

#### 服务端环境
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+)
- **CPU**: 4核心+
- **内存**: 8GB+
- **存储**: SSD 100GB+
- **网络**: 带宽10Mbps+

### 1.2 依赖安装

#### 客户端依赖
```bash
# Python依赖
pip install aiohttp asyncio cryptography sqlite3 pyjwt

# 或使用requirements.txt
pip install -r requirements.txt
```

#### 服务端依赖
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 2. 数据库初始化

### 2.1 客户端SQLite初始化

#### 数据库迁移脚本
```sql
-- migration_001_add_sync_fields.sql
-- 为现有表添加同步字段
ALTER TABLE t_assets ADD COLUMN sync_version INTEGER DEFAULT 1;
ALTER TABLE t_assets ADD COLUMN sync_status INTEGER DEFAULT 0;
ALTER TABLE t_assets ADD COLUMN last_sync_at DATETIME;
ALTER TABLE t_assets ADD COLUMN device_id TEXT;
ALTER TABLE t_assets ADD COLUMN is_deleted INTEGER DEFAULT 0;
ALTER TABLE t_assets ADD COLUMN conflict_data TEXT;

-- migration_002_create_sync_tables.sql
-- 创建同步相关表
CREATE TABLE IF NOT EXISTS sync_config (
    id INTEGER PRIMARY KEY,
    device_id TEXT UNIQUE,
    last_sync_timestamp DATETIME,
    sync_token TEXT,
    server_url TEXT,
    encryption_key TEXT,
    sync_interval INTEGER DEFAULT 300,
    auto_sync_enabled INTEGER DEFAULT 1
);

CREATE TABLE IF NOT EXISTS sync_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type TEXT,
    table_name TEXT,
    record_id TEXT,
    sync_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT,
    error_message TEXT,
    data_snapshot TEXT
);

CREATE TABLE IF NOT EXISTS sync_conflicts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT,
    record_id TEXT,
    local_data TEXT,
    remote_data TEXT,
    conflict_type TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved_at DATETIME,
    resolution_strategy TEXT,
    resolved_data TEXT
);

-- migration_003_create_indexes.sql
-- 创建性能优化索引
CREATE INDEX IF NOT EXISTS idx_assets_sync_status ON t_assets(sync_status);
CREATE INDEX IF NOT EXISTS idx_assets_updated_at ON t_assets(updated_at);
CREATE INDEX IF NOT EXISTS idx_assets_device_id ON t_assets(device_id);
CREATE INDEX IF NOT EXISTS idx_sync_log_timestamp ON sync_log(sync_timestamp);
CREATE INDEX IF NOT EXISTS idx_conflicts_table_record ON sync_conflicts(table_name, record_id);
```

#### 迁移执行脚本
```python
# migrate.py
import sqlite3
import os

def run_migrations(db_path, migrations_dir):
    """执行数据库迁移"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建迁移记录表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version TEXT PRIMARY KEY,
            applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 获取已应用的迁移
    cursor.execute("SELECT version FROM schema_migrations")
    applied_migrations = {row[0] for row in cursor.fetchall()}
    
    # 执行未应用的迁移
    migration_files = sorted([f for f in os.listdir(migrations_dir) if f.endswith('.sql')])
    
    for migration_file in migration_files:
        version = migration_file.replace('.sql', '')
        if version not in applied_migrations:
            print(f"Applying migration: {migration_file}")
            
            with open(os.path.join(migrations_dir, migration_file), 'r') as f:
                migration_sql = f.read()
                
            cursor.executescript(migration_sql)
            cursor.execute("INSERT INTO schema_migrations (version) VALUES (?)", (version,))
            conn.commit()
            
    conn.close()
    print("Migrations completed successfully")

if __name__ == "__main__":
    run_migrations("./data/app.db", "./migrations")
```

### 2.2 服务端数据库配置

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: syncdb
      POSTGRES_USER: syncuser
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      
  sync-api:
    build: .
    environment:
      DATABASE_URL: postgresql://syncuser:${DB_PASSWORD}@postgres:5432/syncdb
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs

volumes:
  postgres_data:
  redis_data:
```

#### 环境变量配置
```bash
# .env
DB_PASSWORD=your_secure_password
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key
API_BASE_URL=https://your-sync-server.com
```

## 3. 客户端配置

### 3.1 配置文件
```json
{
  "sync_config": {
    "server_url": "https://your-sync-server.com",
    "sync_interval": 300,
    "auto_sync_enabled": true,
    "batch_size": 100,
    "max_retries": 3,
    "timeout": 30
  },
  "security": {
    "encryption_enabled": true,
    "tls_verify": true,
    "auth_token_refresh_interval": 3600
  },
  "conflict_resolution": {
    "default_strategy": "timestamp",
    "auto_resolve": true,
    "backup_conflicts": true
  },
  "logging": {
    "level": "INFO",
    "file_path": "./logs/sync.log",
    "max_file_size": "10MB",
    "backup_count": 5
  }
}
```

### 3.2 客户端启动脚本
```python
# sync_client.py
import asyncio
import json
import logging
from sync_engine import SyncEngine

async def main():
    # 加载配置
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, config['logging']['level']),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config['logging']['file_path']),
            logging.StreamHandler()
        ]
    )
    
    # 初始化同步引擎
    sync_engine = SyncEngine(
        db_path='./data/app.db',
        config=config
    )
    
    # 启动同步服务
    await sync_engine.start()

if __name__ == "__main__":
    asyncio.run(main())
```

## 4. 服务端部署

### 4.1 应用部署
```bash
# 部署脚本 deploy.sh
#!/bin/bash

# 设置变量
APP_DIR="/opt/sync-server"
BACKUP_DIR="/opt/backups"
LOG_DIR="/var/log/sync-server"

# 创建目录
sudo mkdir -p $APP_DIR $BACKUP_DIR $LOG_DIR

# 拉取最新代码
cd $APP_DIR
git pull origin main

# 构建和启动服务
docker-compose down
docker-compose build
docker-compose up -d

# 等待服务启动
sleep 30

# 健康检查
curl -f http://localhost:8080/health || exit 1

echo "Deployment completed successfully"
```

### 4.2 Nginx配置
```nginx
# /etc/nginx/sites-available/sync-server
server {
    listen 443 ssl http2;
    server_name your-sync-server.com;
    
    ssl_certificate /etc/ssl/certs/your-cert.pem;
    ssl_certificate_key /etc/ssl/private/your-key.pem;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # API限流
    location /api/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://localhost:8080;
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-sync-server.com;
    return 301 https://$server_name$request_uri;
}
```

## 5. 监控配置

### 5.1 Prometheus配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'sync-server'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s
```

### 5.2 Grafana仪表板
```json
{
  "dashboard": {
    "title": "Sync Server Monitoring",
    "panels": [
      {
        "title": "Sync Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(sync_operations_total{status=\"success\"}[5m]) / rate(sync_operations_total[5m]) * 100"
          }
        ]
      },
      {
        "title": "Active Sync Sessions",
        "type": "graph",
        "targets": [
          {
            "expr": "sync_active_sessions"
          }
        ]
      },
      {
        "title": "Conflict Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(sync_conflicts_total[5m])"
          }
        ]
      }
    ]
  }
}
```

## 6. 备份策略

### 6.1 数据库备份
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# PostgreSQL备份
pg_dump -h localhost -U syncuser syncdb > $BACKUP_DIR/syncdb_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/syncdb_$DATE.sql

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Backup completed: syncdb_$DATE.sql.gz"
```

### 6.2 自动备份配置
```bash
# 添加到crontab
# 每天凌晨2点执行备份
0 2 * * * /opt/scripts/backup.sh >> /var/log/backup.log 2>&1
```

## 7. 故障排查

### 7.1 常见问题
1. **同步失败**: 检查网络连接和服务状态
2. **数据冲突**: 查看冲突日志和解决策略
3. **性能问题**: 监控CPU、内存和数据库性能
4. **认证失败**: 检查JWT令牌和证书配置

### 7.2 日志分析
```bash
# 查看同步错误
grep "ERROR" /var/log/sync-server/app.log | tail -50

# 分析冲突模式
grep "CONFLICT" /var/log/sync-server/app.log | awk '{print $5}' | sort | uniq -c

# 监控同步性能
grep "SYNC_COMPLETED" /var/log/sync-server/app.log | awk '{print $6}' | sort -n
```

这个部署配置指南提供了完整的环境搭建、配置管理、服务部署和监控运维的详细步骤，确保数据同步系统能够稳定可靠地运行。
