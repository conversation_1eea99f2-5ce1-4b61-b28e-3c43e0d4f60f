# 场景5：网络异常与断线重连

## 📱 场景描述

**用户情况**：
- 网络连接不稳定，经常断线重连
- 在网络异常期间继续进行数据操作
- 网络恢复后需要快速同步积压的变更
- 需要处理网络中断导致的数据不一致

**典型场景**：
- 移动网络信号不稳定
- WiFi连接间歇性中断
- 服务器维护导致的临时断线
- 网络拥堵导致的超时
- 跨地域网络延迟

**核心需求**：
1. 自动检测网络状态变化
2. 离线模式下正常工作
3. 网络恢复后自动重连和同步
4. 处理网络异常导致的数据冲突
5. 提供网络状态反馈给用户

## 🎯 解决方案

### 1. 网络状态监控

#### 网络连接检测
```python
class NetworkMonitor:
    def __init__(self, config):
        self.config = config
        self.is_online = True
        self.last_online_time = datetime.now()
        self.connection_quality = 'good'
        self.listeners = []
        
    async def start_monitoring(self):
        """启动网络监控"""
        
        # 启动多种检测方式
        asyncio.create_task(self.ping_based_detection())
        asyncio.create_task(self.api_based_detection())
        asyncio.create_task(self.websocket_based_detection())
        
    async def ping_based_detection(self):
        """基于ping的网络检测"""
        
        while True:
            try:
                # 测试多个目标
                targets = [
                    self.config['sync_server_host'],
                    '*******',  # Google DNS
                    '*******'   # Cloudflare DNS
                ]
                
                ping_results = []
                for target in targets:
                    result = await self.ping_host(target)
                    ping_results.append(result)
                
                # 分析ping结果
                online_count = sum(1 for r in ping_results if r['success'])
                
                if online_count >= 2:
                    await self.set_network_status(True, 'ping')
                    self.connection_quality = self.assess_connection_quality(ping_results)
                else:
                    await self.set_network_status(False, 'ping')
                    
            except Exception as e:
                await self.log_network_error('ping_detection', str(e))
                
            await asyncio.sleep(self.config['ping_interval'])
            
    async def api_based_detection(self):
        """基于API调用的网络检测"""
        
        while True:
            try:
                # 调用轻量级API检测
                response = await self.call_health_check_api()
                
                if response['success']:
                    await self.set_network_status(True, 'api')
                    self.server_latency = response['latency']
                else:
                    await self.set_network_status(False, 'api')
                    
            except Exception as e:
                await self.set_network_status(False, 'api')
                await self.log_network_error('api_detection', str(e))
                
            await asyncio.sleep(self.config['api_check_interval'])
            
    async def set_network_status(self, is_online, detection_method):
        """设置网络状态"""
        
        previous_status = self.is_online
        self.is_online = is_online
        
        if is_online:
            self.last_online_time = datetime.now()
        
        # 状态变化时通知监听器
        if previous_status != is_online:
            await self.notify_status_change(is_online, detection_method)
            
    async def notify_status_change(self, is_online, detection_method):
        """通知网络状态变化"""
        
        event = {
            'type': 'network_status_change',
            'is_online': is_online,
            'detection_method': detection_method,
            'timestamp': datetime.now(),
            'connection_quality': self.connection_quality
        }
        
        for listener in self.listeners:
            try:
                await listener(event)
            except Exception as e:
                await self.log_error(f"Listener error: {e}")
```

#### 连接质量评估
```python
def assess_connection_quality(self, ping_results):
    """评估连接质量"""
    
    successful_pings = [r for r in ping_results if r['success']]
    
    if not successful_pings:
        return 'poor'
    
    avg_latency = sum(r['latency'] for r in successful_pings) / len(successful_pings)
    packet_loss = (len(ping_results) - len(successful_pings)) / len(ping_results)
    
    if avg_latency < 100 and packet_loss < 0.1:
        return 'excellent'
    elif avg_latency < 300 and packet_loss < 0.2:
        return 'good'
    elif avg_latency < 1000 and packet_loss < 0.5:
        return 'fair'
    else:
        return 'poor'
```

### 2. 离线模式管理

#### 离线数据队列
```python
class OfflineDataManager:
    def __init__(self, db):
        self.db = db
        self.offline_queue = []
        self.max_queue_size = 10000
        
    async def queue_offline_operation(self, operation):
        """将操作加入离线队列"""
        
        operation_record = {
            'id': str(uuid.uuid4()),
            'type': operation['type'],  # INSERT, UPDATE, DELETE
            'table_name': operation['table_name'],
            'data': json.dumps(operation['data']),
            'timestamp': datetime.now().isoformat(),
            'retry_count': 0,
            'status': 'pending'
        }
        
        # 存储到本地数据库
        await self.db.execute("""
            INSERT INTO offline_operation_queue 
            (id, operation_type, table_name, operation_data, created_at, status)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            operation_record['id'],
            operation_record['type'],
            operation_record['table_name'],
            operation_record['data'],
            operation_record['timestamp'],
            operation_record['status']
        ))
        
        # 内存队列管理
        self.offline_queue.append(operation_record)
        
        # 队列大小控制
        if len(self.offline_queue) > self.max_queue_size:
            await self.compress_queue()
            
    async def compress_queue(self):
        """压缩离线队列"""
        
        # 合并同一记录的多次操作
        compressed_operations = {}
        
        for operation in self.offline_queue:
            key = f"{operation['table_name']}:{operation['data']['uuid']}"
            
            if key not in compressed_operations:
                compressed_operations[key] = operation
            else:
                # 合并操作
                existing = compressed_operations[key]
                merged = await self.merge_operations(existing, operation)
                compressed_operations[key] = merged
        
        # 更新队列
        self.offline_queue = list(compressed_operations.values())
        
        # 更新数据库
        await self.update_compressed_queue()
```

#### 离线操作执行
```python
async def execute_offline_operation(self, operation):
    """执行离线操作"""
    
    try:
        if operation['type'] == 'INSERT':
            await self.execute_offline_insert(operation)
        elif operation['type'] == 'UPDATE':
            await self.execute_offline_update(operation)
        elif operation['type'] == 'DELETE':
            await self.execute_offline_delete(operation)
            
        # 标记操作为本地完成
        await self.mark_operation_local_complete(operation['id'])
        
    except Exception as e:
        await self.handle_offline_operation_error(operation, e)
        
async def execute_offline_insert(self, operation):
    """执行离线插入操作"""
    
    data = json.loads(operation['data'])
    table_name = operation['table_name']
    
    # 标记为离线创建
    data['sync_status'] = 0  # 待同步
    data['created_offline'] = True
    data['offline_timestamp'] = operation['timestamp']
    
    # 插入本地数据库
    columns = ', '.join(data.keys())
    placeholders = ', '.join(['?' for _ in data])
    
    await self.db.execute(f"""
        INSERT INTO {table_name} ({columns})
        VALUES ({placeholders})
    """, list(data.values()))
```

### 3. 自动重连机制

#### 智能重连策略
```python
class AutoReconnectManager:
    def __init__(self, network_monitor, sync_engine):
        self.network_monitor = network_monitor
        self.sync_engine = sync_engine
        self.reconnect_attempts = 0
        self.max_attempts = 10
        self.base_delay = 1  # 基础延迟秒数
        
    async def start_auto_reconnect(self):
        """启动自动重连"""
        
        # 监听网络状态变化
        self.network_monitor.add_listener(self.on_network_status_change)
        
    async def on_network_status_change(self, event):
        """处理网络状态变化"""
        
        if event['is_online']:
            # 网络恢复，开始重连
            await self.handle_network_recovery()
        else:
            # 网络断开，进入离线模式
            await self.handle_network_disconnection()
            
    async def handle_network_recovery(self):
        """处理网络恢复"""
        
        try:
            # 重置重连计数
            self.reconnect_attempts = 0
            
            # 显示重连状态
            await self.show_reconnection_status("正在重新连接...")
            
            # 验证服务器连接
            server_available = await self.verify_server_connection()
            
            if server_available:
                # 开始同步积压的数据
                await self.sync_pending_data()
                
                # 恢复正常同步
                await self.sync_engine.resume_normal_sync()
                
                await self.show_reconnection_status("重连成功", success=True)
            else:
                # 服务器不可用，稍后重试
                await self.schedule_reconnect_retry()
                
        except Exception as e:
            await self.handle_reconnection_error(e)
            
    async def sync_pending_data(self):
        """同步积压的数据"""
        
        # 获取离线期间的操作队列
        pending_operations = await self.get_pending_operations()
        
        if not pending_operations:
            return
            
        total_operations = len(pending_operations)
        processed = 0
        
        await self.show_sync_progress("正在同步积压数据...", 0, total_operations)
        
        # 批量处理操作
        batch_size = 50
        for i in range(0, len(pending_operations), batch_size):
            batch = pending_operations[i:i + batch_size]
            
            try:
                await self.process_operation_batch(batch)
                processed += len(batch)
                
                progress = (processed / total_operations) * 100
                await self.show_sync_progress(
                    f"已同步 {processed}/{total_operations} 个操作", 
                    progress, 
                    total_operations
                )
                
            except Exception as e:
                await self.handle_batch_sync_error(batch, e)
```

#### 简化重连机制
```python
class SimpleReconnectManager:
    def __init__(self):
        self.max_retries = 5

    async def handle_network_error(self):
        """简化的网络错误处理"""

        # 指数退避 + 最大重试次数
        for attempt in range(self.max_retries):
            await asyncio.sleep(2 ** attempt)  # 1, 2, 4, 8, 16秒

            if await self.test_connection():
                await self.show_status("网络连接已恢复")
                return True

        await self.show_status("网络连接失败，已切换到离线模式")
        return False
```

### 4. 数据同步恢复

#### 增量同步恢复
```python
class SyncRecoveryManager:
    async def recover_sync_after_reconnection(self):
        """重连后恢复同步"""
        
        try:
            # 1. 获取断线前的同步位点
            last_sync_sequence = await self.get_last_sync_sequence()
            disconnect_time = await self.get_last_disconnect_time()
            
            # 2. 分析离线期间的变更
            offline_analysis = await self.analyze_offline_period(
                last_sync_sequence, disconnect_time
            )
            
            # 3. 选择恢复策略
            recovery_strategy = await self.choose_recovery_strategy(offline_analysis)
            
            # 4. 执行恢复
            if recovery_strategy == 'incremental':
                await self.incremental_recovery()
            elif recovery_strategy == 'batch':
                await self.batch_recovery()
            else:
                await self.full_recovery()
                
        except Exception as e:
            await self.handle_recovery_error(e)
            
    async def analyze_offline_period(self, last_sequence, disconnect_time):
        """分析离线期间的情况"""
        
        # 估算错过的变更数量
        estimated_changes = await self.estimate_missed_changes(
            last_sequence, disconnect_time
        )
        
        # 分析本地离线操作
        local_offline_ops = await self.count_local_offline_operations()
        
        # 评估网络质量
        network_quality = self.network_monitor.connection_quality
        
        return {
            'estimated_remote_changes': estimated_changes,
            'local_offline_operations': local_offline_ops,
            'offline_duration': datetime.now() - disconnect_time,
            'network_quality': network_quality,
            'complexity_score': self.calculate_complexity_score(
                estimated_changes, local_offline_ops
            )
        }
```

#### 冲突检测与解决
```python
async def detect_reconnection_conflicts(self):
    """检测重连后的冲突"""
    
    conflicts = []
    
    # 获取离线期间的本地操作
    local_operations = await self.get_local_offline_operations()
    
    # 获取同期的远程变更
    remote_changes = await self.get_remote_changes_during_offline()
    
    # 检测冲突
    for local_op in local_operations:
        record_id = local_op['record_id']
        
        # 查找同一记录的远程变更
        conflicting_remote = [
            r for r in remote_changes 
            if r['record_id'] == record_id
        ]
        
        if conflicting_remote:
            conflict = await self.analyze_operation_conflict(
                local_op, conflicting_remote
            )
            if conflict:
                conflicts.append(conflict)
    
    return conflicts

async def resolve_reconnection_conflicts(self, conflicts):
    """解决重连冲突"""
    
    resolution_results = []
    
    for conflict in conflicts:
        # 根据冲突类型选择解决策略
        if conflict['severity'] == 'low':
            # 自动解决
            resolution = await self.auto_resolve_conflict(conflict)
        else:
            # 需要用户介入
            resolution = await self.user_resolve_conflict(conflict)
            
        resolution_results.append(resolution)
        
        # 应用解决结果
        await self.apply_conflict_resolution(resolution)
    
    return resolution_results
```

### 5. 用户体验优化

#### 网络状态指示器
```python
class NetworkStatusIndicator:
    def __init__(self, ui_manager):
        self.ui = ui_manager
        self.current_status = 'unknown'
        
    async def update_status_indicator(self, network_event):
        """更新网络状态指示器"""
        
        status_info = {
            'is_online': network_event['is_online'],
            'quality': network_event.get('connection_quality', 'unknown'),
            'last_sync': await self.get_last_sync_time(),
            'pending_operations': await self.count_pending_operations()
        }
        
        # 选择合适的图标和颜色
        if status_info['is_online']:
            if status_info['quality'] == 'excellent':
                icon = '🟢'  # 绿色圆点
                color = 'green'
                message = '网络连接良好'
            elif status_info['quality'] == 'good':
                icon = '🟡'  # 黄色圆点
                color = 'orange'
                message = '网络连接一般'
            else:
                icon = '🔴'  # 红色圆点
                color = 'red'
                message = '网络连接较差'
        else:
            icon = '⚫'  # 黑色圆点
            color = 'gray'
            message = '网络已断开'
            
        # 更新UI
        await self.ui.update_status_bar({
            'icon': icon,
            'color': color,
            'message': message,
            'details': status_info
        })
```

#### 离线模式提示
```python
async def show_offline_mode_notification(self):
    """显示离线模式通知"""
    
    notification = {
        'type': 'offline_mode',
        'title': '离线模式',
        'message': '网络连接中断，已切换到离线模式。您的操作将在网络恢复后自动同步。',
        'icon': '📴',
        'actions': [
            {'id': 'retry_connection', 'text': '重试连接'},
            {'id': 'view_pending', 'text': '查看待同步操作'},
            {'id': 'dismiss', 'text': '知道了'}
        ],
        'persistent': True
    }
    
    await self.show_notification(notification)

async def show_reconnection_progress(self, phase, progress):
    """显示重连进度"""
    
    progress_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                    网络重连中                                │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  当前阶段: {phase:<40}                      │
    │                                                             │
    │  进度: [{'█' * int(progress/5)}{'░' * (20-int(progress/5))}] {progress:3.0f}%  │
    │                                                             │
    │  待同步操作: {await self.count_pending_operations()} 个                        │
    │  预计剩余时间: {self.estimate_remaining_time()} 分钟                │
    │                                                             │
    │  [ 取消重连 ]  [ 后台运行 ]                                 │
    └─────────────────────────────────────────────────────────────┘
    """
    
    await self.update_progress_dialog(progress_ui)
```

### 6. 错误处理与恢复

#### 网络错误分类处理
```python
class NetworkErrorHandler:
    def __init__(self):
        self.error_handlers = {
            'ConnectionError': self.handle_connection_error,
            'TimeoutError': self.handle_timeout_error,
            'DNSError': self.handle_dns_error,
            'SSLError': self.handle_ssl_error,
            'ServerError': self.handle_server_error
        }
        
    async def handle_network_error(self, error):
        """处理网络错误"""
        
        error_type = type(error).__name__
        handler = self.error_handlers.get(error_type, self.handle_generic_error)
        
        return await handler(error)
        
    async def handle_connection_error(self, error):
        """处理连接错误"""
        
        return {
            'action': 'retry_with_backoff',
            'message': '网络连接失败，正在重试...',
            'retry_delay': 5,
            'max_retries': 5
        }
        
    async def handle_timeout_error(self, error):
        """处理超时错误"""
        
        return {
            'action': 'increase_timeout_and_retry',
            'message': '网络响应超时，正在调整超时设置...',
            'new_timeout': min(self.current_timeout * 2, 60),
            'retry_delay': 2
        }
        
    async def handle_server_error(self, error):
        """处理服务器错误"""
        
        if '503' in str(error):  # 服务不可用
            return {
                'action': 'wait_and_retry',
                'message': '服务器暂时不可用，稍后重试...',
                'retry_delay': 30
            }
        else:
            return {
                'action': 'report_and_fallback',
                'message': '服务器错误，已切换到离线模式',
                'report_error': True
            }
```

## 📊 方案优势

1. **智能检测**: 多种方式检测网络状态变化
2. **无缝切换**: 自动在在线/离线模式间切换
3. **数据保护**: 离线操作队列确保数据不丢失
4. **智能重连**: 指数退避策略避免网络拥堵
5. **用户友好**: 清晰的状态指示和进度反馈
6. **错误恢复**: 完善的错误分类和处理机制

## 🎯 适用场景

- ✅ 移动办公环境
- ✅ 网络不稳定地区
- ✅ 跨地域访问
- ✅ 服务器维护期间

这个方案专门针对网络异常与断线重连场景，提供了完整的网络状态管理和数据同步恢复能力。
