# 场景10：云端数据损坏或丢失恢复

## 📱 场景描述

**用户情况**：
- 云端服务器故障导致数据损坏或丢失
- 数据库损坏、硬件故障、人为误操作
- 需要从客户端数据重建云端数据
- 多个客户端数据可能不一致，需要合并

**典型场景**：
- 云端数据库服务器硬件故障
- 数据中心灾难导致数据丢失
- 数据库损坏无法恢复
- 误删除云端数据
- 云服务提供商服务中断

**核心需求**：
1. 快速检测云端数据异常
2. 从多个客户端收集数据进行重建
3. 智能合并不同客户端的数据
4. 验证重建数据的完整性
5. 最小化服务中断时间

## 🎯 解决方案

### 1. 云端数据异常检测

#### 数据完整性监控
```python
class CloudDataIntegrityMonitor:
    def __init__(self, db, config):
        self.db = db
        self.config = config
        self.integrity_checks = {
            'data_consistency': self.check_data_consistency,
            'referential_integrity': self.check_referential_integrity,
            'data_corruption': self.check_data_corruption,
            'service_availability': self.check_service_availability
        }
        
    async def perform_integrity_check(self):
        """执行完整性检查"""
        
        check_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'checks': {},
            'issues': [],
            'recommendations': []
        }
        
        for check_name, check_func in self.integrity_checks.items():
            try:
                result = await check_func()
                check_results['checks'][check_name] = result
                
                if not result['passed']:
                    check_results['issues'].extend(result['issues'])
                    if result['severity'] == 'critical':
                        check_results['overall_status'] = 'critical'
                    elif result['severity'] == 'warning' and check_results['overall_status'] == 'healthy':
                        check_results['overall_status'] = 'warning'
                        
            except Exception as e:
                check_results['checks'][check_name] = {
                    'passed': False,
                    'error': str(e),
                    'severity': 'critical'
                }
                check_results['overall_status'] = 'critical'
        
        # 生成修复建议
        if check_results['overall_status'] != 'healthy':
            check_results['recommendations'] = await self.generate_recovery_recommendations(
                check_results['issues']
            )
        
        return check_results

async def check_data_consistency(self):
    """检查数据一致性"""
    
    consistency_result = {
        'passed': True,
        'severity': 'info',
        'issues': [],
        'statistics': {}
    }
    
    try:
        # 检查记录数量异常
        table_counts = await self.get_table_record_counts()
        expected_counts = await self.get_expected_record_counts()
        
        for table_name, actual_count in table_counts.items():
            expected_range = expected_counts.get(table_name, {})
            min_expected = expected_range.get('min', 0)
            max_expected = expected_range.get('max', float('inf'))
            
            if actual_count < min_expected:
                consistency_result['issues'].append({
                    'type': 'RECORD_COUNT_LOW',
                    'table': table_name,
                    'actual': actual_count,
                    'expected_min': min_expected,
                    'severity': 'critical'
                })
                consistency_result['passed'] = False
                consistency_result['severity'] = 'critical'
            elif actual_count > max_expected:
                consistency_result['issues'].append({
                    'type': 'RECORD_COUNT_HIGH',
                    'table': table_name,
                    'actual': actual_count,
                    'expected_max': max_expected,
                    'severity': 'warning'
                })
                if consistency_result['severity'] == 'info':
                    consistency_result['severity'] = 'warning'
        
        # 检查数据时间戳异常
        timestamp_anomalies = await self.detect_timestamp_anomalies()
        if timestamp_anomalies:
            consistency_result['issues'].extend(timestamp_anomalies)
            consistency_result['passed'] = False
            consistency_result['severity'] = 'warning'
        
        consistency_result['statistics'] = table_counts
        
    except Exception as e:
        consistency_result['passed'] = False
        consistency_result['severity'] = 'critical'
        consistency_result['issues'].append({
            'type': 'CHECK_ERROR',
            'message': f"一致性检查失败: {str(e)}"
        })
    
    return consistency_result

async def detect_cloud_data_corruption(self):
    """检测云端数据损坏"""
    
    corruption_indicators = []
    
    # 1. 检查数据库连接
    try:
        await self.test_database_connection()
    except Exception as e:
        corruption_indicators.append({
            'type': 'DATABASE_CONNECTION_FAILED',
            'severity': 'critical',
            'message': f"数据库连接失败: {str(e)}"
        })
    
    # 2. 检查关键表是否存在
    critical_tables = ['t_assets', 'sync_change_log', 'devices']
    for table in critical_tables:
        try:
            await self.verify_table_exists(table)
        except Exception as e:
            corruption_indicators.append({
                'type': 'CRITICAL_TABLE_MISSING',
                'severity': 'critical',
                'table': table,
                'message': f"关键表 {table} 不存在或损坏"
            })
    
    # 3. 检查数据完整性约束
    integrity_violations = await self.check_integrity_constraints()
    corruption_indicators.extend(integrity_violations)
    
    return {
        'corrupted': len(corruption_indicators) > 0,
        'indicators': corruption_indicators,
        'severity': 'critical' if any(i['severity'] == 'critical' for i in corruption_indicators) else 'warning'
    }
```

### 2. 客户端数据收集

#### 数据收集协调器
```python
class ClientDataCollector:
    def __init__(self):
        self.collection_sessions = {}
        
    async def initiate_data_collection(self, collection_id):
        """启动数据收集"""
        
        collection_session = {
            'collection_id': collection_id,
            'start_time': datetime.now(),
            'status': 'COLLECTING',
            'participating_devices': [],
            'collected_data': {},
            'collection_progress': {}
        }
        
        self.collection_sessions[collection_id] = collection_session
        
        # 1. 通知所有活跃设备参与数据收集
        active_devices = await self.get_active_devices()
        
        for device in active_devices:
            try:
                await self.request_device_data_contribution(device['device_id'], collection_id)
                collection_session['participating_devices'].append(device['device_id'])
            except Exception as e:
                await self.log_collection_error(device['device_id'], str(e))
        
        # 2. 设置收集超时
        asyncio.create_task(self.monitor_collection_timeout(collection_id))
        
        return collection_session

async def request_device_data_contribution(self, device_id, collection_id):
    """请求设备贡献数据"""
    
    request_message = {
        'type': 'DATA_COLLECTION_REQUEST',
        'collection_id': collection_id,
        'priority': 'HIGH',
        'requested_tables': ['t_assets', 't_other_table'],
        'include_metadata': True,
        'deadline': (datetime.now() + timedelta(hours=1)).isoformat()
    }
    
    # 通过WebSocket或推送通知发送请求
    await self.send_collection_request(device_id, request_message)

async def receive_device_data_contribution(self, device_id, collection_id, contributed_data):
    """接收设备贡献的数据"""
    
    if collection_id not in self.collection_sessions:
        raise ValueError(f"未知的收集会话: {collection_id}")
    
    session = self.collection_sessions[collection_id]
    
    # 验证数据完整性
    validation_result = await self.validate_contributed_data(contributed_data)
    
    if validation_result['valid']:
        # 存储贡献的数据
        session['collected_data'][device_id] = {
            'data': contributed_data,
            'received_at': datetime.now().isoformat(),
            'validation_score': validation_result['score'],
            'metadata': contributed_data.get('metadata', {})
        }
        
        # 更新收集进度
        await self.update_collection_progress(collection_id)
        
        # 检查是否收集完成
        if await self.is_collection_complete(collection_id):
            await self.finalize_data_collection(collection_id)
    else:
        await self.handle_invalid_contribution(device_id, collection_id, validation_result)

async def validate_contributed_data(self, data):
    """验证贡献的数据"""
    
    validation_result = {
        'valid': True,
        'score': 100,
        'issues': []
    }
    
    # 1. 检查数据结构
    required_fields = ['tables', 'metadata', 'device_info']
    for field in required_fields:
        if field not in data:
            validation_result['issues'].append(f"缺少必需字段: {field}")
            validation_result['score'] -= 20
    
    # 2. 检查数据时效性
    if 'metadata' in data:
        last_update = data['metadata'].get('last_update')
        if last_update:
            update_time = datetime.fromisoformat(last_update)
            age_hours = (datetime.now() - update_time).total_seconds() / 3600
            
            if age_hours > 24:
                validation_result['issues'].append(f"数据过期: {age_hours:.1f}小时前")
                validation_result['score'] -= min(30, age_hours)
    
    # 3. 检查数据完整性
    if 'tables' in data:
        for table_name, table_data in data['tables'].items():
            if not isinstance(table_data, list):
                validation_result['issues'].append(f"表 {table_name} 数据格式错误")
                validation_result['score'] -= 15
    
    validation_result['valid'] = validation_result['score'] >= 60
    
    return validation_result
```

### 3. 数据合并与重建

#### 智能数据合并算法
```python
class CloudDataRebuilder:
    async def rebuild_cloud_data(self, collection_id):
        """重建云端数据"""
        
        collection_session = self.collection_sessions[collection_id]
        collected_data = collection_session['collected_data']
        
        rebuild_result = {
            'rebuild_id': str(uuid.uuid4()),
            'start_time': datetime.now().isoformat(),
            'source_devices': list(collected_data.keys()),
            'rebuilt_tables': {},
            'conflicts_resolved': 0,
            'total_records': 0
        }
        
        try:
            # 1. 分析数据质量和可信度
            await self.update_rebuild_progress("分析数据质量...", 10)
            data_quality_analysis = await self.analyze_data_quality(collected_data)
            
            # 2. 按表合并数据
            tables_to_rebuild = self.get_tables_from_collected_data(collected_data)
            
            for i, table_name in enumerate(tables_to_rebuild):
                table_progress_start = 20 + (i / len(tables_to_rebuild)) * 60
                table_progress_end = 20 + ((i + 1) / len(tables_to_rebuild)) * 60
                
                await self.update_rebuild_progress(f"重建表 {table_name}...", table_progress_start)
                
                table_rebuild_result = await self.rebuild_table_data(
                    table_name, 
                    collected_data, 
                    data_quality_analysis
                )
                
                rebuild_result['rebuilt_tables'][table_name] = table_rebuild_result
                rebuild_result['conflicts_resolved'] += table_rebuild_result['conflicts_resolved']
                rebuild_result['total_records'] += table_rebuild_result['record_count']
                
                await self.update_rebuild_progress(f"表 {table_name} 重建完成", table_progress_end)
            
            # 3. 验证重建结果
            await self.update_rebuild_progress("验证重建结果...", 85)
            validation_result = await self.validate_rebuilt_data(rebuild_result)
            
            if validation_result['valid']:
                # 4. 应用重建数据到云端
                await self.update_rebuild_progress("应用重建数据...", 95)
                await self.apply_rebuilt_data_to_cloud(rebuild_result)
                
                await self.update_rebuild_progress("云端数据重建完成", 100)
                rebuild_result['success'] = True
            else:
                raise RebuildError("重建数据验证失败")
                
        except Exception as e:
            rebuild_result['success'] = False
            rebuild_result['error'] = str(e)
            await self.handle_rebuild_error(e, rebuild_result)
            raise
        
        return rebuild_result

async def rebuild_table_data(self, table_name, collected_data, quality_analysis):
    """重建单个表的数据"""
    
    # 1. 收集所有设备的表数据
    table_data_sources = {}
    for device_id, device_data in collected_data.items():
        if table_name in device_data['data']['tables']:
            table_data_sources[device_id] = {
                'records': device_data['data']['tables'][table_name],
                'quality_score': quality_analysis[device_id]['score'],
                'last_update': device_data['data']['metadata'].get('last_update')
            }
    
    # 2. 构建记录索引（按UUID）
    record_index = {}
    for device_id, source_data in table_data_sources.items():
        for record in source_data['records']:
            record_uuid = record.get('uuid')
            if record_uuid:
                if record_uuid not in record_index:
                    record_index[record_uuid] = []
                
                record_index[record_uuid].append({
                    'device_id': device_id,
                    'record': record,
                    'quality_score': source_data['quality_score'],
                    'last_update': source_data['last_update']
                })
    
    # 3. 合并冲突记录
    merged_records = []
    conflicts_resolved = 0
    
    for record_uuid, record_versions in record_index.items():
        if len(record_versions) == 1:
            # 无冲突，直接使用
            merged_records.append(record_versions[0]['record'])
        else:
            # 有冲突，需要合并
            merged_record = await self.resolve_record_conflict(record_versions)
            merged_records.append(merged_record)
            conflicts_resolved += 1
    
    return {
        'record_count': len(merged_records),
        'conflicts_resolved': conflicts_resolved,
        'merged_records': merged_records,
        'source_devices': list(table_data_sources.keys())
    }

async def resolve_record_conflict(self, record_versions):
    """解决记录冲突"""
    
    # 按质量分数和时间排序
    sorted_versions = sorted(record_versions, key=lambda x: (
        x['quality_score'], 
        x['last_update'] or '1970-01-01'
    ), reverse=True)
    
    # 使用最高质量的记录作为基础
    base_record = sorted_versions[0]['record'].copy()
    
    # 字段级合并
    for version in sorted_versions[1:]:
        record = version['record']
        
        # 合并非空字段
        for field, value in record.items():
            if field not in base_record or not base_record[field]:
                if value:  # 只合并非空值
                    base_record[field] = value
    
    # 更新合并元数据
    base_record['merged_from_devices'] = [v['device_id'] for v in record_versions]
    base_record['merge_timestamp'] = datetime.now().isoformat()
    
    return base_record
```

### 4. 数据验证与恢复

#### 重建数据验证
```python
async def validate_rebuilt_data(self, rebuild_result):
    """验证重建的数据"""
    
    validation_result = {
        'valid': True,
        'issues': [],
        'statistics': {},
        'confidence_score': 100
    }
    
    try:
        # 1. 检查数据完整性
        for table_name, table_result in rebuild_result['rebuilt_tables'].items():
            # 检查记录数量合理性
            record_count = table_result['record_count']
            expected_range = await self.get_expected_record_range(table_name)
            
            if record_count < expected_range['min']:
                validation_result['issues'].append({
                    'type': 'INSUFFICIENT_RECORDS',
                    'table': table_name,
                    'count': record_count,
                    'expected_min': expected_range['min']
                })
                validation_result['confidence_score'] -= 20
            
            # 检查数据质量
            quality_issues = await self.check_data_quality(table_result['merged_records'])
            if quality_issues:
                validation_result['issues'].extend(quality_issues)
                validation_result['confidence_score'] -= len(quality_issues) * 5
        
        # 2. 检查关联完整性
        referential_issues = await self.check_referential_integrity(rebuild_result)
        if referential_issues:
            validation_result['issues'].extend(referential_issues)
            validation_result['confidence_score'] -= len(referential_issues) * 10
        
        # 3. 计算最终验证结果
        validation_result['valid'] = validation_result['confidence_score'] >= 70
        validation_result['statistics'] = {
            'total_records': rebuild_result['total_records'],
            'conflicts_resolved': rebuild_result['conflicts_resolved'],
            'confidence_score': validation_result['confidence_score']
        }
        
    except Exception as e:
        validation_result['valid'] = False
        validation_result['issues'].append({
            'type': 'VALIDATION_ERROR',
            'message': f"验证过程失败: {str(e)}"
        })
    
    return validation_result

async def apply_rebuilt_data_to_cloud(self, rebuild_result):
    """将重建的数据应用到云端"""
    
    try:
        # 1. 备份当前云端数据（如果还有的话）
        backup_result = await self.backup_current_cloud_data()
        
        # 2. 清空损坏的数据
        await self.clear_corrupted_cloud_data()
        
        # 3. 重建数据库结构
        await self.rebuild_database_schema()
        
        # 4. 导入重建的数据
        for table_name, table_result in rebuild_result['rebuilt_tables'].items():
            await self.import_table_data(table_name, table_result['merged_records'])
        
        # 5. 重建索引和约束
        await self.rebuild_database_indexes()
        
        # 6. 验证导入结果
        import_validation = await self.validate_imported_data(rebuild_result)
        
        if not import_validation['valid']:
            # 导入失败，尝试恢复备份
            if backup_result['success']:
                await self.restore_from_backup(backup_result['backup_id'])
            raise RebuildError("数据导入验证失败")
        
        # 7. 更新系统状态
        await self.update_system_recovery_status('RECOVERED')
        
    except Exception as e:
        await self.update_system_recovery_status('FAILED')
        raise RebuildError(f"应用重建数据失败: {str(e)}")
```

### 5. 灾难恢复流程

#### 完整恢复流程
```python
async def execute_disaster_recovery(self):
    """执行灾难恢复流程"""
    
    recovery_session = {
        'session_id': str(uuid.uuid4()),
        'start_time': datetime.now().isoformat(),
        'phase': 'DETECTION',
        'status': 'IN_PROGRESS'
    }
    
    try:
        # 阶段1: 损坏检测
        await self.update_recovery_status("检测数据损坏程度...", 5)
        corruption_assessment = await self.assess_data_corruption()
        
        if corruption_assessment['severity'] == 'total_loss':
            # 完全丢失，启动完整重建
            recovery_session['recovery_type'] = 'FULL_REBUILD'
            await self.execute_full_data_rebuild(recovery_session)
        elif corruption_assessment['severity'] == 'partial_corruption':
            # 部分损坏，尝试修复
            recovery_session['recovery_type'] = 'PARTIAL_REPAIR'
            await self.execute_partial_data_repair(recovery_session)
        else:
            # 轻微问题，数据修复
            recovery_session['recovery_type'] = 'DATA_REPAIR'
            await self.execute_data_repair(recovery_session)
        
        recovery_session['status'] = 'COMPLETED'
        await self.update_recovery_status("灾难恢复完成", 100)
        
    except Exception as e:
        recovery_session['status'] = 'FAILED'
        recovery_session['error'] = str(e)
        await self.handle_recovery_failure(recovery_session, e)
        raise
    
    return recovery_session
```

## 📊 方案优势

1. **快速检测**: 自动监控云端数据完整性
2. **智能收集**: 从多个客户端收集数据进行重建
3. **冲突解决**: 智能合并不同来源的数据
4. **质量保证**: 完整的数据验证和质量评估
5. **灾难恢复**: 完整的灾难恢复流程
6. **最小中断**: 快速恢复服务可用性

## 🎯 适用场景

- ✅ 云端服务器故障
- ✅ 数据库损坏
- ✅ 数据中心灾难
- ✅ 人为误操作
- ✅ 服务提供商故障

这个方案提供了完整的云端数据保护和灾难恢复能力，确保即使在最严重的数据丢失情况下也能快速恢复服务。
