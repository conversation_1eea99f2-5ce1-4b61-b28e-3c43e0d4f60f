任务核心需求：
    1、同步本地客户端 sqlite中的表数据到云端。支持本地数据同步到云端和云端同步到其他本地客户端。
    2、需要完整的同步方案，包含当前的调整，以及数据冲突的处理等。
    3、方案要符合通用的软件数据备份能力要求以及合规。
    4、不需要给出项目代码，只需要完善方案即可。    
现状分析：
   1、当前表结构如下，仅为一个表，其他表类似。
      CREATE TABLE t\_assets (   id INTEGER PRIMARY KEY AUTOINCREMENT,             -- 模型ID   created\_at DATETIME DEFAULT CURRENT\_TIMESTAMP,    -- 创建时间   updated\_at DATETIME DEFAULT CURRENT\_TIMESTAMP,    -- 更新时间   label TEXT,                                       -- 名称   asset\_ip TEXT,                                    -- 服务器IP   group\_name TEXT,                                  -- 分组名称   uuid TEXT UNIQUE,                                 -- 唯一ID   auth\_type TEXT,                                   -- 认证方式   port INTEGER,                                     -- 端口   username TEXT,                                    -- 用户名   password TEXT,                                    -- 密码   key\_chain\_id INTEGER,                             -- 密钥链ID   favorite  INTEGER                                 -- 是否收藏 , asset\_type TEXT DEFAULT 'person');
   2、