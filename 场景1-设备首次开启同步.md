# 场景1：新设备首次开启同步

## 📱 场景描述

**用户情况**：
- 购买了新电脑/手机
- 安装了应用程序
- 本地可能已有一些数据（如导入的配置、手动添加的资产等）
- 第一次开启云同步功能

**核心需求**：
1. 将云端已有数据同步到新设备
2. 将新设备本地数据备份到云端
3. 处理本地与云端的数据冲突
4. 建立后续的自动同步机制

## 🎯 解决方案

### 1. 用户引导流程

#### 同步向导界面
```
┌─────────────────────────────────────┐
│          欢迎使用云同步功能          │
├─────────────────────────────────────┤
│                                     │
│  检测到您的设备上已有 15 条资产数据  │
│  云端已有 128 条资产数据            │
│                                     │
│  请选择同步方式：                    │
│                                     │
│  ○ 使用云端数据 (推荐)              │
│    下载云端数据，本地数据作为新增    │
│                                     │
│  ○ 使用本地数据                     │
│    上传本地数据，覆盖云端冲突数据    │
│                                     │
│  ○ 智能合并                         │
│    自动合并本地和云端数据            │
│                                     │
│  ○ 手动选择                         │
│    逐个处理冲突数据                  │
│                                     │
│  [ 开始同步 ]  [ 稍后设置 ]         │
└─────────────────────────────────────┘
```

### 2. 技术实现方案

#### 设备注册与认证
```python
class NewDeviceSyncWizard:
    async def start_sync_setup(self):
        """启动新设备同步设置向导"""
        
        # 1. 用户登录认证
        user_info = await self.authenticate_user()
        
        # 2. 设备注册
        device_id = await self.register_device(user_info)
        
        # 3. 分析数据情况
        analysis = await self.analyze_data_situation()
        
        # 4. 显示同步选项
        strategy = await self.show_sync_options(analysis)
        
        # 5. 执行选择的同步策略
        await self.execute_sync_strategy(strategy)
        
        return device_id
        
    async def analyze_data_situation(self):
        """分析本地和云端数据情况"""
        
        # 分析本地数据
        local_analysis = {
            'total_records': self.count_local_records(),
            'tables': self.analyze_local_tables(),
            'last_modified': self.get_local_last_modified(),
            'unique_records': self.count_unique_local_records()
        }
        
        # 获取云端数据概览
        cloud_analysis = await self.get_cloud_data_summary()
        
        return {
            'local': local_analysis,
            'cloud': cloud_analysis,
            'recommendation': self.get_sync_recommendation(local_analysis, cloud_analysis)
        }
```

#### 同步策略实现

##### 策略1：云端数据优先
```python
async def sync_cloud_first(self):
    """云端数据优先同步策略"""
    
    progress_callback = self.update_progress
    
    try:
        # 步骤1: 备份本地数据
        await progress_callback("正在备份本地数据...", 10)
        backup_path = await self.backup_local_data()
        
        # 步骤2: 下载云端数据
        await progress_callback("正在下载云端数据...", 30)
        cloud_data = await self.download_cloud_data_paginated()
        
        # 步骤3: 应用云端数据
        await progress_callback("正在应用云端数据...", 60)
        await self.apply_cloud_data(cloud_data)
        
        # 步骤4: 处理本地特有数据
        await progress_callback("正在处理本地特有数据...", 80)
        unique_local = await self.find_unique_local_data(backup_path)
        if unique_local:
            await self.upload_unique_local_data(unique_local)
        
        # 步骤5: 完成设置
        await progress_callback("正在完成同步设置...", 95)
        await self.finalize_sync_setup()
        
        await progress_callback("同步完成！", 100)
        
    except Exception as e:
        await self.handle_sync_error(e, backup_path)
        raise
```

##### 策略2：本地数据优先
```python
async def sync_local_first(self):
    """本地数据优先同步策略"""
    
    try:
        # 步骤1: 上传本地数据
        await self.update_progress("正在上传本地数据...", 20)
        upload_result = await self.upload_all_local_data()
        
        # 步骤2: 处理上传冲突
        if upload_result.get('conflicts'):
            await self.update_progress("正在处理数据冲突...", 50)
            await self.resolve_upload_conflicts(upload_result['conflicts'], 'local_wins')
        
        # 步骤3: 下载云端特有数据
        await self.update_progress("正在下载云端特有数据...", 70)
        cloud_unique = await self.get_cloud_unique_data()
        if cloud_unique:
            await self.import_cloud_unique_data(cloud_unique)
        
        # 步骤4: 完成设置
        await self.update_progress("正在完成同步设置...", 90)
        await self.finalize_sync_setup()
        
        await self.update_progress("同步完成！", 100)
        
    except Exception as e:
        await self.handle_sync_error(e)
        raise
```

##### 策略3：智能合并
```python
async def sync_smart_merge(self):
    """智能合并同步策略"""
    
    try:
        # 步骤1: 获取所有数据
        await self.update_progress("正在分析数据...", 20)
        local_data = await self.get_all_local_data()
        cloud_data = await self.download_cloud_data_paginated()
        
        # 步骤2: 检测冲突
        await self.update_progress("正在检测数据冲突...", 40)
        conflicts = await self.detect_all_conflicts(local_data, cloud_data)
        
        # 步骤3: 自动解决冲突
        await self.update_progress("正在智能合并数据...", 60)
        resolved_data = await self.auto_resolve_conflicts(conflicts)
        
        # 步骤4: 应用合并结果
        await self.update_progress("正在应用合并结果...", 80)
        await self.apply_merged_data(resolved_data)
        
        # 步骤5: 处理无冲突数据
        await self.update_progress("正在处理其他数据...", 90)
        await self.handle_non_conflict_data(local_data, cloud_data, conflicts)
        
        await self.update_progress("同步完成！", 100)
        
    except Exception as e:
        await self.handle_sync_error(e)
        raise
```

##### 策略4：用户手动选择
```python
async def sync_user_choice(self):
    """用户手动选择同步策略"""
    
    try:
        # 步骤1: 检测所有冲突
        await self.update_progress("正在检测数据冲突...", 30)
        conflicts = await self.detect_all_conflicts()
        
        if not conflicts:
            # 无冲突，直接合并
            await self.sync_smart_merge()
            return
        
        # 步骤2: 用户逐个处理冲突
        await self.update_progress("等待用户处理冲突...", 50)
        resolved_conflicts = []
        
        for i, conflict in enumerate(conflicts):
            resolution = await self.show_conflict_resolution_ui(conflict)
            resolved_conflicts.append(resolution)
            
            progress = 50 + (i + 1) / len(conflicts) * 40
            await self.update_progress(f"已处理 {i+1}/{len(conflicts)} 个冲突", progress)
        
        # 步骤3: 应用用户选择
        await self.update_progress("正在应用用户选择...", 95)
        await self.apply_user_resolutions(resolved_conflicts)
        
        await self.update_progress("同步完成！", 100)
        
    except Exception as e:
        await self.handle_sync_error(e)
        raise
```

### 3. 冲突处理界面

#### 冲突解决UI设计
```
┌─────────────────────────────────────────────────────────────┐
│                    数据冲突处理 (1/3)                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  资产名称冲突：                                              │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   本地数据      │    │   云端数据      │                │
│  ├─────────────────┤    ├─────────────────┤                │
│  │ 名称: 生产服务器 │    │ 名称: 主服务器   │                │
│  │ IP: ************│    │ IP: ************│                │
│  │ 端口: 22        │    │ 端口: 22        │                │
│  │ 更新: 1月12日   │    │ 更新: 1月10日   │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
│  请选择处理方式：                                            │
│  ○ 使用本地数据 (生产服务器)                                 │
│  ○ 使用云端数据 (主服务器)                                   │
│  ○ 手动合并数据                                             │
│                                                             │
│  [ 上一个 ]  [ 跳过 ]  [ 下一个 ]  [ 应用到全部 ]          │
└─────────────────────────────────────────────────────────────┘
```

### 4. 数据库操作

#### 本地数据备份
```sql
-- 创建备份表
CREATE TABLE t_assets_backup_20240115 AS 
SELECT * FROM t_assets;

-- 记录备份信息
INSERT INTO sync_backups (
    backup_name,
    table_name, 
    record_count,
    created_at,
    backup_reason
) VALUES (
    't_assets_backup_20240115',
    't_assets',
    (SELECT COUNT(*) FROM t_assets),
    CURRENT_TIMESTAMP,
    'initial_sync_backup'
);
```

#### 数据应用和回滚
```python
class DataManager:
    async def apply_cloud_data_safely(self, cloud_data):
        """安全地应用云端数据"""
        
        transaction = await self.db.begin()
        try:
            # 1. 清空现有数据
            await self.db.execute("DELETE FROM t_assets")
            
            # 2. 插入云端数据
            for record in cloud_data:
                await self.insert_record(record)
            
            # 3. 更新同步状态
            await self.update_sync_status('completed')
            
            await transaction.commit()
            
        except Exception as e:
            await transaction.rollback()
            # 从备份恢复
            await self.restore_from_backup()
            raise
            
    async def restore_from_backup(self):
        """从备份恢复数据"""
        
        backup_table = f"t_assets_backup_{datetime.now().strftime('%Y%m%d')}"
        
        await self.db.execute("DELETE FROM t_assets")
        await self.db.execute(f"""
            INSERT INTO t_assets 
            SELECT * FROM {backup_table}
        """)
```

### 5. 错误处理和恢复

#### 同步失败处理
```python
async def handle_sync_error(self, error, backup_path=None):
    """处理同步错误"""
    
    error_info = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'timestamp': datetime.now(),
        'device_id': self.device_id
    }
    
    # 记录错误日志
    await self.log_sync_error(error_info)
    
    # 尝试恢复
    if backup_path:
        try:
            await self.restore_from_backup(backup_path)
            await self.show_error_message(
                "同步失败，已恢复到原始状态。请检查网络连接后重试。"
            )
        except:
            await self.show_error_message(
                "同步失败且无法自动恢复。请联系技术支持。"
            )
    else:
        await self.show_error_message(
            "同步失败。请检查网络连接后重试。"
        )
```

### 6. 成功完成流程

#### 同步完成设置
```python
async def finalize_sync_setup(self):
    """完成同步设置"""
    
    # 1. 标记初始同步完成
    await self.db.execute("""
        UPDATE sync_config 
        SET initial_sync_completed = 1,
            sync_status = 'ACTIVE',
            last_sync_timestamp = CURRENT_TIMESTAMP
        WHERE device_id = ?
    """, (self.device_id,))
    
    # 2. 启用自动同步
    await self.enable_auto_sync()
    
    # 3. 清理临时数据
    await self.cleanup_temp_data()
    
    # 4. 显示完成界面
    await self.show_completion_summary()
```

## 📊 方案优势

1. **用户友好**: 清晰的向导界面，用户容易理解和操作
2. **数据安全**: 自动备份，支持错误恢复
3. **灵活选择**: 4种策略适应不同用户需求
4. **智能处理**: 自动检测和解决大部分冲突
5. **进度可视**: 实时显示同步进度和状态

## 🎯 适用场景

- ✅ 新购买设备首次使用
- ✅ 重装系统后恢复数据
- ✅ 从其他设备迁移数据
- ✅ 多设备间数据统一

这个方案专门针对新设备首次开启同步的场景，提供了完整的用户体验和技术实现。
