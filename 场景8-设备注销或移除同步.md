# 场景8：设备注销或移除同步

## 📱 场景描述

**用户情况**：
- 用户不再使用某个设备（如更换新设备、设备损坏、转让设备）
- 需要从云端移除设备的同步权限
- 需要清理设备相关的同步数据和历史记录
- 可能需要将设备数据迁移到其他设备

**典型场景**：
- 更换新设备，旧设备不再使用
- 设备丢失或被盗，需要远程注销
- 员工离职，需要移除其设备访问权限
- 设备转让给他人，需要清除个人数据
- 临时设备使用完毕后清理

**核心需求**：
1. 安全地注销设备同步权限
2. 清理云端设备相关数据
3. 可选的数据迁移到其他设备
4. 防止已注销设备继续访问数据
5. 提供设备管理和监控功能

## 🎯 解决方案

### 1. 设备注销流程

#### 主动注销（用户操作）
```python
class DeviceDeregistrationManager:
    async def initiate_device_deregistration(self, device_id, deregistration_type):
        """启动设备注销流程"""
        
        deregistration_options = {
            'normal': self.normal_deregistration,      # 正常注销
            'transfer': self.transfer_deregistration,  # 数据迁移注销
            'emergency': self.emergency_deregistration, # 紧急注销（设备丢失）
            'cleanup': self.cleanup_deregistration     # 清理注销（彻底删除）
        }
        
        handler = deregistration_options.get(deregistration_type, self.normal_deregistration)
        return await handler(device_id)
        
    async def normal_deregistration(self, device_id):
        """正常设备注销"""
        
        try:
            # 1. 验证用户权限
            await self.verify_deregistration_permission(device_id)
            
            # 2. 显示注销确认界面
            confirmation = await self.show_deregistration_confirmation(device_id)
            
            if not confirmation['confirmed']:
                return {'success': False, 'message': '用户取消注销'}
            
            # 3. 最后一次数据同步
            await self.update_progress("正在进行最后一次数据同步...", 10)
            final_sync_result = await self.perform_final_sync(device_id)
            
            # 4. 备份设备数据
            await self.update_progress("正在备份设备数据...", 30)
            backup_result = await self.backup_device_data(device_id)
            
            # 5. 撤销设备权限
            await self.update_progress("正在撤销设备权限...", 60)
            await self.revoke_device_permissions(device_id)
            
            # 6. 清理本地同步配置
            await self.update_progress("正在清理本地配置...", 80)
            await self.cleanup_local_sync_config()
            
            # 7. 记录注销日志
            await self.log_device_deregistration(device_id, 'normal', backup_result)
            
            await self.update_progress("设备注销完成", 100)
            
            return {
                'success': True,
                'backup_id': backup_result['backup_id'],
                'deregistration_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            await self.handle_deregistration_error(e, device_id)
            raise

async def show_deregistration_confirmation(self, device_id):
    """显示注销确认界面"""
    
    device_info = await self.get_device_info(device_id)
    sync_stats = await self.get_device_sync_stats(device_id)
    
    confirmation_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                    设备注销确认                              │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  即将注销设备：{device_info['device_name']:<30}              │
    │  注册时间：{device_info['registered_at']}                   │
    │  最后同步：{device_info['last_sync_at']}                    │
    │                                                             │
    │  📊 同步统计：                                              │
    │  • 同步记录数：{sync_stats['total_records']} 条                        │
    │  • 同步次数：{sync_stats['sync_count']} 次                           │
    │  • 数据大小：{sync_stats['data_size']}                      │
    │                                                             │
    │  ⚠️  注销后将发生：                                          │
    │  • 设备将无法继续同步数据                                    │
    │  • 本地同步配置将被清除                                      │
    │  • 设备数据将自动备份到云端                                  │
    │  • 可以稍后恢复到其他设备                                    │
    │                                                             │
    │  □ 同时清除本地所有数据                                      │
    │  □ 将数据迁移到其他设备                                      │
    │                                                             │
    │  [ 确认注销 ]  [ 取消 ]                                     │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_confirmation_dialog(confirmation_ui)
```

#### 紧急注销（设备丢失/被盗）
```python
async def emergency_deregistration(self, device_id):
    """紧急设备注销（设备丢失/被盗）"""
    
    try:
        # 1. 立即撤销所有权限
        await self.immediately_revoke_all_permissions(device_id)
        
        # 2. 标记设备为丢失状态
        await self.mark_device_as_lost(device_id)
        
        # 3. 生成新的加密密钥（使旧设备无法解密数据）
        await self.rotate_encryption_keys(exclude_device=device_id)
        
        # 4. 通知其他设备更新密钥
        await self.notify_other_devices_key_rotation(device_id)
        
        # 5. 创建安全备份
        backup_result = await self.create_security_backup(device_id)
        
        # 6. 记录安全事件
        await self.log_security_incident('device_lost', device_id)
        
        return {
            'success': True,
            'emergency_response': True,
            'backup_id': backup_result['backup_id'],
            'new_encryption_key_id': backup_result['key_id']
        }
        
    except Exception as e:
        await self.handle_emergency_deregistration_error(e, device_id)
        raise

async def immediately_revoke_all_permissions(self, device_id):
    """立即撤销设备的所有权限"""
    
    # 1. 撤销API访问权限
    await self.revoke_api_access_tokens(device_id)
    
    # 2. 撤销WebSocket连接
    await self.terminate_websocket_connections(device_id)
    
    # 3. 加入黑名单
    await self.add_to_device_blacklist(device_id)
    
    # 4. 更新权限数据库
    await self.db.execute("""
        UPDATE devices 
        SET sync_status = 'REVOKED', 
            revoked_at = CURRENT_TIMESTAMP,
            revocation_reason = 'EMERGENCY'
        WHERE device_id = ?
    """, (device_id,))
```

### 2. 数据迁移功能

#### 设备间数据迁移
```python
class DeviceDataMigration:
    async def migrate_device_data(self, source_device_id, target_device_id):
        """设备间数据迁移"""
        
        try:
            # 1. 验证迁移权限
            await self.verify_migration_permissions(source_device_id, target_device_id)
            
            # 2. 分析迁移数据
            migration_analysis = await self.analyze_migration_data(source_device_id)
            
            # 3. 显示迁移预览
            user_confirmation = await self.show_migration_preview(migration_analysis)
            
            if not user_confirmation['confirmed']:
                return {'success': False, 'message': '用户取消迁移'}
            
            # 4. 执行数据迁移
            await self.update_migration_progress("正在迁移数据...", 20)
            
            migration_result = await self.execute_data_migration(
                source_device_id, 
                target_device_id, 
                migration_analysis
            )
            
            # 5. 验证迁移结果
            await self.update_migration_progress("正在验证迁移结果...", 80)
            
            validation_result = await self.validate_migration_result(migration_result)
            
            if validation_result['success']:
                # 6. 注销源设备
                await self.update_migration_progress("正在注销源设备...", 90)
                await self.deregister_source_device(source_device_id)
                
                await self.update_migration_progress("迁移完成", 100)
                
                return {
                    'success': True,
                    'migrated_records': migration_result['record_count'],
                    'target_device': target_device_id
                }
            else:
                raise MigrationError("迁移验证失败")
                
        except Exception as e:
            await self.handle_migration_error(e, source_device_id, target_device_id)
            raise

async def show_migration_preview(self, analysis):
    """显示迁移预览"""
    
    preview_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                    数据迁移预览                              │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  源设备：{analysis['source_device']['name']:<35}            │
    │  目标设备：{analysis['target_device']['name']:<33}          │
    │                                                             │
    │  📊 迁移数据统计：                                          │
    │  ┌─────────────────────────────────────────────────────────┐ │
    │  │ 表名        │ 记录数  │ 大小     │ 冲突数  │ 操作     │ │
    │  ├─────────────────────────────────────────────────────────┤ │
    │  │ t_assets    │ {analysis['tables']['t_assets']['count']:>6} │ {analysis['tables']['t_assets']['size']:>7} │ {analysis['tables']['t_assets']['conflicts']:>6} │ 合并     │ │
    │  │ t_configs   │ {analysis['tables']['t_configs']['count']:>6} │ {analysis['tables']['t_configs']['size']:>7} │ {analysis['tables']['t_configs']['conflicts']:>6} │ 替换     │ │
    │  └─────────────────────────────────────────────────────────┘ │
    │                                                             │
    │  ⚠️  迁移策略：                                              │
    │  • 无冲突数据：直接迁移                                      │
    │  • 冲突数据：使用源设备数据                                  │
    │  • 配置数据：合并配置项                                      │
    │                                                             │
    │  预计耗时：{analysis['estimated_duration']} 分钟                        │
    │                                                             │
    │  [ 开始迁移 ]  [ 修改策略 ]  [ 取消 ]                      │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_migration_dialog(preview_ui)
```

### 3. 设备管理功能

#### 设备列表管理
```python
class DeviceManagementConsole:
    async def show_device_management_console(self):
        """显示设备管理控制台"""
        
        devices = await self.get_user_devices()
        
        console_ui = f"""
        ┌─────────────────────────────────────────────────────────────┐
        │                    设备管理控制台                            │
        ├─────────────────────────────────────────────────────────────┤
        │                                                             │
        │  📱 已注册设备 ({len(devices)} 个)：                         │
        │                                                             │
        """
        
        for device in devices:
            status_icon = self.get_device_status_icon(device['sync_status'])
            console_ui += f"""│  {status_icon} {device['device_name']:<25} │ {device['last_active']:<15} │ [管理] │
        """
        
        console_ui += f"""│                                                             │
        │  🔧 批量操作：                                              │
        │  [ 批量注销 ]  [ 导出设备列表 ]  [ 安全审计 ]              │
        │                                                             │
        │  📊 统计信息：                                              │
        │  • 活跃设备：{len([d for d in devices if d['sync_status'] == 'ACTIVE'])} 个                                        │
        │  • 离线设备：{len([d for d in devices if d['sync_status'] == 'INACTIVE'])} 个                                      │
        │  • 已注销设备：{len([d for d in devices if d['sync_status'] == 'REVOKED'])} 个                                    │
        │                                                             │
        │  [ 添加设备 ]  [ 刷新列表 ]  [ 关闭 ]                      │
        └─────────────────────────────────────────────────────────────┘
        """
        
        return await self.show_management_console(console_ui, devices)

async def batch_device_operations(self, operation_type, device_ids):
    """批量设备操作"""
    
    operations = {
        'deregister': self.batch_deregister_devices,
        'backup': self.batch_backup_devices,
        'audit': self.batch_audit_devices
    }
    
    if operation_type not in operations:
        raise ValueError(f"不支持的批量操作: {operation_type}")
    
    return await operations[operation_type](device_ids)

async def batch_deregister_devices(self, device_ids):
    """批量注销设备"""
    
    results = []
    total_devices = len(device_ids)
    
    for i, device_id in enumerate(device_ids):
        try:
            progress = (i / total_devices) * 100
            await self.update_batch_progress(f"正在注销设备 {i+1}/{total_devices}", progress)
            
            result = await self.normal_deregistration(device_id)
            results.append({
                'device_id': device_id,
                'success': result['success'],
                'backup_id': result.get('backup_id')
            })
            
        except Exception as e:
            results.append({
                'device_id': device_id,
                'success': False,
                'error': str(e)
            })
    
    return {
        'total_processed': len(device_ids),
        'successful': len([r for r in results if r['success']]),
        'failed': len([r for r in results if not r['success']]),
        'results': results
    }
```

### 4. 安全和审计功能

#### 设备访问审计
```python
class DeviceSecurityAuditor:
    async def perform_device_security_audit(self, device_id=None):
        """执行设备安全审计"""
        
        audit_results = {
            'audit_time': datetime.now().isoformat(),
            'device_security': [],
            'access_anomalies': [],
            'recommendations': []
        }
        
        devices_to_audit = [device_id] if device_id else await self.get_all_device_ids()
        
        for device_id in devices_to_audit:
            device_audit = await self.audit_single_device(device_id)
            audit_results['device_security'].append(device_audit)
            
            # 检测访问异常
            anomalies = await self.detect_access_anomalies(device_id)
            if anomalies:
                audit_results['access_anomalies'].extend(anomalies)
        
        # 生成安全建议
        audit_results['recommendations'] = await self.generate_security_recommendations(
            audit_results
        )
        
        return audit_results

async def audit_single_device(self, device_id):
    """审计单个设备"""
    
    device_info = await self.get_device_info(device_id)
    
    audit_result = {
        'device_id': device_id,
        'device_name': device_info['device_name'],
        'security_score': 0,
        'issues': [],
        'last_activity': device_info['last_active_at']
    }
    
    # 检查设备状态
    if device_info['sync_status'] != 'ACTIVE':
        audit_result['issues'].append({
            'type': 'STATUS_ISSUE',
            'severity': 'medium',
            'message': f"设备状态异常: {device_info['sync_status']}"
        })
    
    # 检查最后活跃时间
    last_active = datetime.fromisoformat(device_info['last_active_at'])
    days_inactive = (datetime.now() - last_active).days
    
    if days_inactive > 30:
        audit_result['issues'].append({
            'type': 'INACTIVE_DEVICE',
            'severity': 'high',
            'message': f"设备已{days_inactive}天未活跃，建议注销"
        })
    
    # 检查访问模式
    access_pattern = await self.analyze_access_pattern(device_id)
    if access_pattern['suspicious']:
        audit_result['issues'].append({
            'type': 'SUSPICIOUS_ACCESS',
            'severity': 'high',
            'message': "检测到可疑访问模式"
        })
    
    # 计算安全评分
    audit_result['security_score'] = self.calculate_security_score(audit_result['issues'])
    
    return audit_result
```

## 📊 方案优势

1. **安全注销**: 多种注销方式适应不同场景
2. **数据保护**: 注销前自动备份，防止数据丢失
3. **紧急响应**: 支持设备丢失的紧急处理
4. **数据迁移**: 便捷的设备间数据迁移功能
5. **集中管理**: 统一的设备管理控制台
6. **安全审计**: 完整的设备访问审计功能

## 🎯 适用场景

- ✅ 设备更换升级
- ✅ 设备丢失被盗
- ✅ 员工离职管理
- ✅ 设备转让清理
- ✅ 安全合规审计

这个方案提供了完整的设备生命周期管理，确保数据安全和访问控制。
