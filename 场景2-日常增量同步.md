# 场景2：日常增量同步

## 📱 场景描述

**用户情况**：
- 设备已完成初始同步设置
- 用户在日常使用中进行各种操作：新增、修改、删除资产
- 多个设备同时在线，需要实时保持数据一致性
- 网络状况良好，设备正常在线

**核心需求**：
1. 实时或准实时同步数据变更
2. 最小化网络传输和系统资源消耗
3. 处理并发修改冲突
4. 保证数据一致性和完整性

## 🎯 解决方案

### 1. 增量同步机制

#### 变更检测策略
```python
class IncrementalSyncEngine:
    def __init__(self, db_path, config):
        self.db = sqlite3.connect(db_path)
        self.config = config
        self.device_id = self.get_device_id()
        self.last_sync_sequence = self.get_last_sync_sequence()
        
    async def start_incremental_sync(self):
        """启动增量同步"""
        
        while True:
            try:
                # 1. 检查是否有本地变更需要上传
                await self.push_local_changes()
                
                # 2. 检查是否有远程变更需要下载
                await self.pull_remote_changes()
                
                # 3. 处理可能的冲突
                await self.resolve_any_conflicts()
                
                # 4. 等待下次同步
                await asyncio.sleep(self.config['sync_interval'])
                
            except Exception as e:
                await self.handle_sync_error(e)
                await asyncio.sleep(self.config['error_retry_interval'])
```

#### 本地变更检测
```python
async def detect_local_changes(self):
    """检测本地数据变更"""
    
    last_sync_time = self.get_last_sync_timestamp()
    
    # 查询所有变更记录
    changes = {
        'inserts': [],
        'updates': [], 
        'deletes': []
    }
    
    # 检测新增记录
    sql_inserts = """
    SELECT * FROM t_assets 
    WHERE created_at > ? AND sync_status = 0
    """
    changes['inserts'] = self.db.execute(sql_inserts, (last_sync_time,)).fetchall()
    
    # 检测修改记录
    sql_updates = """
    SELECT * FROM t_assets 
    WHERE updated_at > ? AND created_at <= ? AND sync_status = 0
    """
    changes['updates'] = self.db.execute(sql_updates, (last_sync_time, last_sync_time)).fetchall()
    
    # 检测删除记录
    sql_deletes = """
    SELECT * FROM t_assets 
    WHERE is_deleted = 1 AND updated_at > ?
    """
    changes['deletes'] = self.db.execute(sql_deletes, (last_sync_time,)).fetchall()
    
    return changes
```

### 2. 实时同步触发机制

#### 数据库触发器
```sql
-- 创建变更记录表
CREATE TABLE local_change_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT,
    record_id TEXT,
    operation_type TEXT, -- INSERT, UPDATE, DELETE
    change_data TEXT,    -- JSON格式的变更数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    sync_status INTEGER DEFAULT 0  -- 0:待同步, 1:已同步
);

-- 为t_assets表创建触发器
CREATE TRIGGER tr_assets_insert 
AFTER INSERT ON t_assets
BEGIN
    INSERT INTO local_change_queue (table_name, record_id, operation_type, change_data)
    VALUES ('t_assets', NEW.uuid, 'INSERT', json_object(
        'id', NEW.id,
        'uuid', NEW.uuid,
        'label', NEW.label,
        'asset_ip', NEW.asset_ip,
        'created_at', NEW.created_at,
        'updated_at', NEW.updated_at
    ));
END;

CREATE TRIGGER tr_assets_update 
AFTER UPDATE ON t_assets
BEGIN
    INSERT INTO local_change_queue (table_name, record_id, operation_type, change_data)
    VALUES ('t_assets', NEW.uuid, 'UPDATE', json_object(
        'id', NEW.id,
        'uuid', NEW.uuid,
        'label', NEW.label,
        'asset_ip', NEW.asset_ip,
        'updated_at', NEW.updated_at,
        'old_updated_at', OLD.updated_at
    ));
END;

CREATE TRIGGER tr_assets_delete 
AFTER UPDATE ON t_assets
WHEN NEW.is_deleted = 1 AND OLD.is_deleted = 0
BEGIN
    INSERT INTO local_change_queue (table_name, record_id, operation_type, change_data)
    VALUES ('t_assets', NEW.uuid, 'DELETE', json_object(
        'id', NEW.id,
        'uuid', NEW.uuid,
        'deleted_at', NEW.updated_at
    ));
END;
```

#### 变更队列处理
```python
class ChangeQueueProcessor:
    async def process_change_queue(self):
        """处理本地变更队列"""
        
        # 获取待同步的变更
        pending_changes = self.db.execute("""
            SELECT * FROM local_change_queue 
            WHERE sync_status = 0 
            ORDER BY created_at ASC
            LIMIT 100
        """).fetchall()
        
        if not pending_changes:
            return
        
        # 按表分组处理
        changes_by_table = {}
        for change in pending_changes:
            table = change['table_name']
            if table not in changes_by_table:
                changes_by_table[table] = []
            changes_by_table[table].append(change)
        
        # 批量上传每个表的变更
        for table_name, table_changes in changes_by_table.items():
            await self.upload_table_changes(table_name, table_changes)
```

### 3. 批量上传优化

#### 智能批量策略
```python
async def upload_table_changes(self, table_name, changes):
    """智能批量上传表变更"""
    
    # 合并同一记录的多次变更
    merged_changes = self.merge_duplicate_changes(changes)
    
    # 按操作类型分组
    grouped_changes = {
        'inserts': [],
        'updates': [],
        'deletes': []
    }
    
    for change in merged_changes:
        op_type = change['operation_type'].lower()
        if op_type in grouped_changes:
            grouped_changes[op_type].append(change)
    
    # 构建上传请求
    upload_request = {
        'device_id': self.device_id,
        'table_name': table_name,
        'changes': grouped_changes,
        'sync_token': self.get_sync_token()
    }
    
    # 上传到云端
    response = await self.api_client.upload_changes(upload_request)
    
    # 处理上传结果
    await self.handle_upload_response(response, changes)

def merge_duplicate_changes(self, changes):
    """合并同一记录的重复变更"""
    
    # 按record_id分组
    changes_by_record = {}
    for change in changes:
        record_id = change['record_id']
        if record_id not in changes_by_record:
            changes_by_record[record_id] = []
        changes_by_record[record_id].append(change)
    
    merged = []
    for record_id, record_changes in changes_by_record.items():
        if len(record_changes) == 1:
            merged.append(record_changes[0])
        else:
            # 合并多个变更为最终状态
            final_change = self.merge_record_changes(record_changes)
            merged.append(final_change)
    
    return merged
```

### 4. 下行同步处理

#### 拉取远程变更
```python
async def pull_remote_changes(self):
    """拉取远程变更"""
    
    # 获取当前同步位点
    last_sequence = self.get_last_sync_sequence()
    
    # 请求远程变更
    response = await self.api_client.get_changes_since_sequence({
        'device_id': self.device_id,
        'last_sequence': last_sequence,
        'limit': 1000
    })
    
    remote_changes = response.get('changes', [])
    
    if remote_changes:
        # 按序列号排序
        remote_changes.sort(key=lambda x: x['sequence_id'])
        
        # 逐个应用变更
        for change in remote_changes:
            await self.apply_remote_change(change)
        
        # 更新同步位点
        latest_sequence = remote_changes[-1]['sequence_id']
        self.update_sync_sequence(latest_sequence)

async def apply_remote_change(self, change):
    """应用远程变更"""
    
    operation = change['operation_type']
    table_name = change['table_name']
    change_data = change['change_data']
    
    # 检查冲突
    conflict = await self.detect_change_conflict(change)
    if conflict:
        await self.handle_conflict(conflict)
        return
    
    # 应用变更
    if operation == 'INSERT':
        await self.apply_remote_insert(table_name, change_data)
    elif operation == 'UPDATE':
        await self.apply_remote_update(table_name, change_data)
    elif operation == 'DELETE':
        await self.apply_remote_delete(table_name, change_data)
    
    # 记录应用日志
    await self.log_change_applied(change)
```

### 5. 冲突检测与处理

#### 实时冲突检测
```python
async def detect_change_conflict(self, remote_change):
    """检测变更冲突"""
    
    record_id = remote_change['record_id']
    remote_data = remote_change['change_data']
    
    # 获取本地当前数据
    local_record = await self.get_local_record(record_id)
    
    if not local_record:
        return None  # 本地无此记录，无冲突
    
    # 检查是否有未同步的本地变更
    local_pending = await self.has_pending_local_changes(record_id)
    if local_pending:
        return {
            'type': 'CONCURRENT_MODIFICATION',
            'record_id': record_id,
            'local_data': local_record,
            'remote_data': remote_data,
            'conflict_fields': self.find_conflict_fields(local_record, remote_data)
        }
    
    return None

async def handle_conflict(self, conflict):
    """处理数据冲突"""
    
    conflict_type = conflict['type']
    
    if conflict_type == 'CONCURRENT_MODIFICATION':
        # 并发修改冲突
        resolution = await self.resolve_concurrent_modification(conflict)
    else:
        # 其他类型冲突
        resolution = await self.resolve_generic_conflict(conflict)
    
    # 应用冲突解决结果
    await self.apply_conflict_resolution(resolution)
    
    # 记录冲突日志
    await self.log_conflict_resolution(conflict, resolution)
```

#### 自动冲突解决策略
```python
async def resolve_concurrent_modification(self, conflict):
    """解决并发修改冲突"""
    
    local_data = conflict['local_data']
    remote_data = conflict['remote_data']
    
    # 获取用户配置的冲突解决策略
    strategy = self.config.get('conflict_resolution_strategy', 'timestamp')
    
    if strategy == 'timestamp':
        # 时间戳策略：选择最新的
        if remote_data['updated_at'] > local_data['updated_at']:
            winner = 'remote'
            final_data = remote_data
        else:
            winner = 'local'
            final_data = local_data
            
    elif strategy == 'server_wins':
        # 服务器优先策略
        winner = 'remote'
        final_data = remote_data
        
    elif strategy == 'local_wins':
        # 本地优先策略
        winner = 'local'
        final_data = local_data
        
    elif strategy == 'field_merge':
        # 字段级合并策略
        winner = 'merged'
        final_data = await self.merge_field_level(local_data, remote_data)
    
    return {
        'strategy': strategy,
        'winner': winner,
        'final_data': final_data,
        'requires_upload': winner == 'local' or winner == 'merged'
    }
```

### 6. 性能优化

#### 连接池管理
```python
class SyncConnectionManager:
    def __init__(self, config):
        self.config = config
        self.connection_pool = aiohttp.TCPConnector(
            limit=10,
            limit_per_host=5,
            keepalive_timeout=30
        )
        self.session = aiohttp.ClientSession(
            connector=self.connection_pool,
            timeout=aiohttp.ClientTimeout(total=30)
        )
    
    async def make_request(self, method, url, **kwargs):
        """发起HTTP请求"""
        async with self.session.request(method, url, **kwargs) as response:
            return await response.json()
```

#### 数据压缩传输
```python
async def upload_changes_compressed(self, changes):
    """压缩上传变更数据"""
    
    # 序列化数据
    json_data = json.dumps(changes, ensure_ascii=False)
    
    # GZIP压缩
    compressed_data = gzip.compress(json_data.encode('utf-8'))
    
    # 计算压缩比
    compression_ratio = len(compressed_data) / len(json_data.encode('utf-8'))
    
    # 只有压缩效果明显时才使用压缩
    if compression_ratio < 0.8:
        headers = {'Content-Encoding': 'gzip'}
        data = compressed_data
    else:
        headers = {}
        data = json_data.encode('utf-8')
    
    return await self.api_client.upload_with_headers(data, headers)
```

### 7. 监控和诊断

#### 同步状态监控
```python
class SyncMonitor:
    def __init__(self):
        self.metrics = {
            'sync_success_count': 0,
            'sync_error_count': 0,
            'conflict_count': 0,
            'avg_sync_duration': 0,
            'last_sync_time': None
        }
    
    async def record_sync_success(self, duration):
        """记录同步成功"""
        self.metrics['sync_success_count'] += 1
        self.metrics['last_sync_time'] = datetime.now()
        
        # 更新平均同步时长
        current_avg = self.metrics['avg_sync_duration']
        count = self.metrics['sync_success_count']
        self.metrics['avg_sync_duration'] = (current_avg * (count - 1) + duration) / count
    
    def get_sync_health_status(self):
        """获取同步健康状态"""
        
        total_syncs = self.metrics['sync_success_count'] + self.metrics['sync_error_count']
        if total_syncs == 0:
            return 'unknown'
        
        success_rate = self.metrics['sync_success_count'] / total_syncs
        
        if success_rate >= 0.95:
            return 'healthy'
        elif success_rate >= 0.8:
            return 'warning'
        else:
            return 'critical'
```

## 📊 方案优势

1. **高效性**: 只同步变更数据，最小化网络传输
2. **实时性**: 基于触发器的实时变更检测
3. **可靠性**: 完善的冲突检测和解决机制
4. **可扩展性**: 支持多表、多设备并发同步
5. **可监控**: 完整的同步状态监控和诊断

## 🎯 适用场景

- ✅ 日常办公数据同步
- ✅ 多设备协同工作
- ✅ 实时数据备份
- ✅ 团队数据共享

### 7. 大数据量同步处理

#### 大数据量检测与策略
```python
class LargeDataSyncHandler:
    def __init__(self, config):
        self.config = config
        self.large_sync_threshold = config.get('large_sync_threshold', 10000)  # 10k记录
        self.batch_size_large = config.get('batch_size_large', 1000)

    async def handle_large_data_sync(self, changes):
        """处理大数据量同步"""

        total_changes = sum(len(table_changes) for table_changes in changes.values())

        if total_changes > self.large_sync_threshold:
            return await self.execute_large_data_strategy(changes, total_changes)
        else:
            return await self.execute_normal_sync(changes)

    async def execute_large_data_strategy(self, changes, total_changes):
        """执行大数据量同步策略"""

        # 1. 显示大数据量同步提示
        user_choice = await self.show_large_sync_confirmation(total_changes)

        if user_choice == 'background':
            return await self.background_large_sync(changes)
        elif user_choice == 'scheduled':
            return await self.schedule_large_sync(changes)
        elif user_choice == 'chunked':
            return await self.chunked_large_sync(changes)
        else:
            return {'success': False, 'reason': 'user_cancelled'}

async def show_large_sync_confirmation(self, total_changes):
    """显示大数据量同步确认"""

    estimated_time = self.estimate_sync_time(total_changes)
    estimated_bandwidth = self.estimate_bandwidth_usage(total_changes)

    confirmation_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                  大数据量同步                                │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  检测到大量数据需要同步：                                    │
    │                                                             │
    │  📊 同步统计：                                              │
    │  • 变更记录数：{total_changes:,} 条                                │
    │  • 预计耗时：{estimated_time} 分钟                          │
    │  • 预计流量：{estimated_bandwidth}                          │
    │                                                             │
    │  🔧 同步方式：                                              │
    │  ○ 后台同步 - 不影响正常使用                                │
    │  ○ 分块同步 - 分批次逐步同步                                │
    │  ○ 定时同步 - 安排在空闲时间                                │
    │  ○ 取消同步 - 稍后手动触发                                  │
    │                                                             │
    │  💡 建议：选择后台同步以获得最佳体验                         │
    │                                                             │
    │  [ 开始同步 ]  [ 高级选项 ]  [ 取消 ]                      │
    └─────────────────────────────────────────────────────────────┘
    """

    return await self.show_sync_options_dialog(confirmation_ui)
```

#### 分块同步实现
```python
async def chunked_large_sync(self, changes):
    """分块大数据量同步"""

    # 1. 计算分块策略
    chunk_plan = await self.calculate_chunk_strategy(changes)

    sync_result = {
        'total_chunks': len(chunk_plan['chunks']),
        'completed_chunks': 0,
        'failed_chunks': [],
        'total_records': chunk_plan['total_records']
    }

    # 2. 显示分块同步进度
    await self.show_chunked_sync_progress(sync_result)

    # 3. 逐块执行同步
    for i, chunk in enumerate(chunk_plan['chunks']):
        try:
            chunk_result = await self.sync_data_chunk(chunk, i + 1, len(chunk_plan['chunks']))
            sync_result['completed_chunks'] += 1

            # 更新进度
            progress = (sync_result['completed_chunks'] / sync_result['total_chunks']) * 100
            await self.update_chunked_progress(f"已完成 {sync_result['completed_chunks']}/{sync_result['total_chunks']} 块", progress)

            # 块间延迟，避免服务器压力
            if i < len(chunk_plan['chunks']) - 1:
                await asyncio.sleep(self.config.get('chunk_delay', 2))

        except Exception as e:
            sync_result['failed_chunks'].append({
                'chunk_index': i,
                'error': str(e),
                'chunk_data': chunk
            })

            # 决定是否继续
            if len(sync_result['failed_chunks']) > 3:
                break

    # 4. 处理失败的块
    if sync_result['failed_chunks']:
        await self.handle_failed_chunks(sync_result['failed_chunks'])

    return sync_result

async def calculate_chunk_strategy(self, changes):
    """计算分块策略"""

    chunk_plan = {
        'chunks': [],
        'total_records': 0,
        'strategy': 'balanced'  # balanced, size_based, table_based
    }

    # 按表分组并计算大小
    table_sizes = {}
    for table_name, table_changes in changes.items():
        total_table_changes = len(table_changes.get('inserts', [])) + \
                            len(table_changes.get('updates', [])) + \
                            len(table_changes.get('deletes', []))
        table_sizes[table_name] = total_table_changes
        chunk_plan['total_records'] += total_table_changes

    # 根据策略创建分块
    if chunk_plan['strategy'] == 'table_based':
        # 按表分块
        for table_name, table_changes in changes.items():
            if table_sizes[table_name] > 0:
                chunk_plan['chunks'].append({
                    'type': 'table',
                    'table_name': table_name,
                    'changes': {table_name: table_changes},
                    'estimated_size': table_sizes[table_name]
                })
    else:
        # 平衡分块
        target_chunk_size = self.batch_size_large
        current_chunk = {'changes': {}, 'estimated_size': 0}

        for table_name, table_changes in changes.items():
            if current_chunk['estimated_size'] + table_sizes[table_name] > target_chunk_size:
                if current_chunk['estimated_size'] > 0:
                    chunk_plan['chunks'].append(current_chunk)
                current_chunk = {'changes': {}, 'estimated_size': 0}

            current_chunk['changes'][table_name] = table_changes
            current_chunk['estimated_size'] += table_sizes[table_name]

        if current_chunk['estimated_size'] > 0:
            chunk_plan['chunks'].append(current_chunk)

    return chunk_plan
```

#### 后台同步实现
```python
async def background_large_sync(self, changes):
    """后台大数据量同步"""

    # 1. 创建后台同步任务
    background_task = {
        'task_id': str(uuid.uuid4()),
        'start_time': datetime.now(),
        'status': 'RUNNING',
        'changes': changes,
        'progress': 0
    }

    # 2. 启动后台任务
    asyncio.create_task(self.execute_background_sync_task(background_task))

    # 3. 显示后台同步通知
    await self.show_background_sync_notification(background_task)

    return {
        'success': True,
        'sync_mode': 'background',
        'task_id': background_task['task_id']
    }

async def execute_background_sync_task(self, task):
    """执行后台同步任务"""

    try:
        # 降低同步优先级，避免影响用户操作
        await self.set_sync_priority('low')

        # 使用较小的批次大小
        original_batch_size = self.config['batch_size']
        self.config['batch_size'] = self.config.get('background_batch_size', 100)

        # 执行同步
        total_changes = sum(len(table_changes) for table_changes in task['changes'].values())
        processed = 0

        for table_name, table_changes in task['changes'].items():
            table_result = await self.sync_table_changes_background(table_name, table_changes)
            processed += table_result['processed_count']

            # 更新进度
            task['progress'] = (processed / total_changes) * 100
            await self.update_background_task_progress(task)

            # 后台任务间隔，避免占用过多资源
            await asyncio.sleep(1)

        task['status'] = 'COMPLETED'
        await self.notify_background_sync_completion(task)

    except Exception as e:
        task['status'] = 'FAILED'
        task['error'] = str(e)
        await self.handle_background_sync_error(task, e)
    finally:
        # 恢复原始配置
        self.config['batch_size'] = original_batch_size
        await self.set_sync_priority('normal')
```

#### 性能优化策略
```python
class LargeSyncOptimizer:
    async def optimize_large_sync_performance(self, changes):
        """优化大数据量同步性能"""

        optimizations = []

        # 1. 数据压缩
        if self.should_use_compression(changes):
            optimizations.append('compression')

        # 2. 并行上传
        if self.can_use_parallel_upload():
            optimizations.append('parallel_upload')

        # 3. 增量压缩
        if self.should_use_delta_compression(changes):
            optimizations.append('delta_compression')

        # 4. 连接池优化
        optimizations.append('connection_pooling')

        return optimizations

async def apply_compression_optimization(self, data):
    """应用压缩优化"""

    # 选择最佳压缩算法
    compression_methods = ['gzip', 'lz4', 'zstd']
    best_method = await self.select_best_compression(data, compression_methods)

    compressed_data = await self.compress_data(data, best_method)

    return {
        'compressed_data': compressed_data,
        'compression_method': best_method,
        'compression_ratio': len(compressed_data) / len(data),
        'original_size': len(data),
        'compressed_size': len(compressed_data)
    }

async def parallel_upload_chunks(self, chunks):
    """并行上传数据块"""

    # 限制并发数，避免服务器压力
    max_concurrent = min(self.config.get('max_concurrent_uploads', 3), len(chunks))

    semaphore = asyncio.Semaphore(max_concurrent)

    async def upload_chunk_with_semaphore(chunk):
        async with semaphore:
            return await self.upload_single_chunk(chunk)

    # 并行执行上传
    upload_tasks = [upload_chunk_with_semaphore(chunk) for chunk in chunks]
    results = await asyncio.gather(*upload_tasks, return_exceptions=True)

    # 处理结果
    successful_uploads = []
    failed_uploads = []

    for i, result in enumerate(results):
        if isinstance(result, Exception):
            failed_uploads.append({'chunk_index': i, 'error': str(result)})
        else:
            successful_uploads.append(result)

    return {
        'successful_count': len(successful_uploads),
        'failed_count': len(failed_uploads),
        'failed_uploads': failed_uploads
    }
```

这个方案专门针对日常增量同步场景，提供了高效、可靠的实时同步能力，并特别加强了大数据量同步的处理能力。
