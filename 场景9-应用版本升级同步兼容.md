# 场景9：应用版本升级同步兼容

## 📱 场景描述

**用户情况**：
- 应用程序发布新版本，包含数据库结构变更
- 不同设备可能运行不同版本的应用
- 需要保证新旧版本间的同步兼容性
- 数据库schema需要平滑升级

**典型场景**：
- 应用添加新字段或新表
- 修改现有字段类型或约束
- 删除废弃的字段或表
- 重构数据结构
- API接口版本升级

**核心需求**：
1. 向前兼容：新版本能处理旧版本数据
2. 向后兼容：旧版本能忽略新版本字段
3. 平滑升级：数据库结构自动迁移
4. 版本检测：自动识别版本差异
5. 降级支持：必要时支持版本回退

## 🎯 解决方案

### 1. 版本管理机制

#### 版本兼容性矩阵
```python
class VersionCompatibilityManager:
    def __init__(self):
        self.compatibility_matrix = {
            '1.0.0': {
                'min_compatible': '1.0.0',
                'max_compatible': '1.2.9',
                'schema_version': 1,
                'api_version': 'v1'
            },
            '1.1.0': {
                'min_compatible': '1.0.0',
                'max_compatible': '1.2.9', 
                'schema_version': 2,
                'api_version': 'v1'
            },
            '1.2.0': {
                'min_compatible': '1.0.0',
                'max_compatible': '1.2.9',
                'schema_version': 3,
                'api_version': 'v1'
            },
            '2.0.0': {
                'min_compatible': '1.2.0',
                'max_compatible': '2.9.9',
                'schema_version': 4,
                'api_version': 'v2'
            }
        }
        
    async def check_version_compatibility(self, local_version, remote_version):
        """检查版本兼容性"""
        
        local_info = self.compatibility_matrix.get(local_version)
        remote_info = self.compatibility_matrix.get(remote_version)
        
        if not local_info or not remote_info:
            return {
                'compatible': False,
                'reason': 'unknown_version',
                'action': 'update_required'
            }
        
        # 检查API版本兼容性
        if local_info['api_version'] != remote_info['api_version']:
            return {
                'compatible': False,
                'reason': 'api_version_mismatch',
                'action': 'major_update_required',
                'local_api': local_info['api_version'],
                'remote_api': remote_info['api_version']
            }
        
        # 检查版本范围兼容性
        local_min = version.parse(local_info['min_compatible'])
        local_max = version.parse(local_info['max_compatible'])
        remote_ver = version.parse(remote_version)
        
        if local_min <= remote_ver <= local_max:
            return {
                'compatible': True,
                'schema_upgrade_needed': local_info['schema_version'] < remote_info['schema_version'],
                'local_schema': local_info['schema_version'],
                'remote_schema': remote_info['schema_version']
            }
        else:
            return {
                'compatible': False,
                'reason': 'version_out_of_range',
                'action': 'update_recommended',
                'supported_range': f"{local_info['min_compatible']} - {local_info['max_compatible']}"
            }

async def handle_version_incompatibility(self, compatibility_result):
    """处理版本不兼容"""
    
    if compatibility_result['reason'] == 'api_version_mismatch':
        await self.show_major_update_required_dialog(compatibility_result)
    elif compatibility_result['reason'] == 'version_out_of_range':
        await self.show_update_recommended_dialog(compatibility_result)
    elif compatibility_result['reason'] == 'unknown_version':
        await self.show_unknown_version_dialog(compatibility_result)
```

#### 版本升级检测
```python
async def detect_and_handle_version_upgrade(self):
    """检测并处理版本升级"""
    
    try:
        # 1. 获取当前应用版本
        current_version = self.get_current_app_version()
        
        # 2. 获取数据库中记录的版本
        stored_version = await self.get_stored_app_version()
        
        # 3. 检查是否需要升级
        if stored_version is None:
            # 首次安装
            await self.handle_first_installation(current_version)
        elif version.parse(current_version) > version.parse(stored_version):
            # 版本升级
            await self.handle_version_upgrade(stored_version, current_version)
        elif version.parse(current_version) < version.parse(stored_version):
            # 版本降级
            await self.handle_version_downgrade(stored_version, current_version)
        
        # 4. 更新存储的版本号
        await self.update_stored_app_version(current_version)
        
    except Exception as e:
        await self.handle_version_detection_error(e)
        raise

async def handle_version_upgrade(self, old_version, new_version):
    """处理版本升级"""
    
    upgrade_path = await self.calculate_upgrade_path(old_version, new_version)
    
    await self.show_upgrade_progress_dialog(f"正在从 {old_version} 升级到 {new_version}")
    
    for step in upgrade_path:
        try:
            await self.execute_upgrade_step(step)
            await self.update_upgrade_progress(step['description'], step['progress'])
        except Exception as e:
            await self.handle_upgrade_step_error(step, e)
            raise
    
    await self.finalize_version_upgrade(new_version)
```

### 2. 数据库Schema迁移

#### 迁移脚本管理
```python
class DatabaseMigrationManager:
    def __init__(self, db):
        self.db = db
        self.migrations = {
            1: self.migrate_to_schema_v1,
            2: self.migrate_to_schema_v2,
            3: self.migrate_to_schema_v3,
            4: self.migrate_to_schema_v4
        }
        
    async def migrate_database_schema(self, target_schema_version):
        """迁移数据库Schema"""
        
        current_schema = await self.get_current_schema_version()
        
        if current_schema == target_schema_version:
            return {'success': True, 'message': '无需迁移'}
        
        if current_schema > target_schema_version:
            return await self.downgrade_schema(current_schema, target_schema_version)
        
        # 执行升级迁移
        migration_steps = list(range(current_schema + 1, target_schema_version + 1))
        
        for step_version in migration_steps:
            try:
                await self.update_migration_progress(f"迁移到Schema v{step_version}", 
                                                   (step_version - current_schema) / len(migration_steps) * 100)
                
                migration_func = self.migrations.get(step_version)
                if migration_func:
                    await migration_func()
                    await self.update_schema_version(step_version)
                else:
                    raise MigrationError(f"找不到Schema v{step_version}的迁移脚本")
                    
            except Exception as e:
                await self.rollback_migration(current_schema)
                raise MigrationError(f"Schema迁移失败: {e}")
        
        return {'success': True, 'migrated_to': target_schema_version}

async def migrate_to_schema_v2(self):
    """迁移到Schema版本2（添加同步字段）"""
    
    # 为现有表添加同步相关字段
    migration_sql = [
        "ALTER TABLE t_assets ADD COLUMN sync_version INTEGER DEFAULT 1",
        "ALTER TABLE t_assets ADD COLUMN sync_status INTEGER DEFAULT 0", 
        "ALTER TABLE t_assets ADD COLUMN last_sync_at DATETIME",
        "ALTER TABLE t_assets ADD COLUMN device_id TEXT",
        "ALTER TABLE t_assets ADD COLUMN is_deleted INTEGER DEFAULT 0",
        
        # 创建同步配置表
        """CREATE TABLE IF NOT EXISTS sync_config (
            id INTEGER PRIMARY KEY,
            device_id TEXT UNIQUE,
            last_sync_timestamp DATETIME,
            sync_token TEXT,
            app_version TEXT,
            schema_version INTEGER
        )""",
        
        # 创建索引
        "CREATE INDEX IF NOT EXISTS idx_assets_sync_status ON t_assets(sync_status)",
        "CREATE INDEX IF NOT EXISTS idx_assets_device_id ON t_assets(device_id)"
    ]
    
    for sql in migration_sql:
        await self.db.execute(sql)
    
    await self.db.commit()

async def migrate_to_schema_v3(self):
    """迁移到Schema版本3（添加变更队列）"""
    
    migration_sql = [
        """CREATE TABLE IF NOT EXISTS local_change_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            table_name TEXT,
            record_id TEXT,
            operation_type TEXT,
            change_data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            sync_status INTEGER DEFAULT 0
        )""",
        
        """CREATE TABLE IF NOT EXISTS sync_conflicts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            table_name TEXT,
            record_id TEXT,
            local_data TEXT,
            remote_data TEXT,
            conflict_type TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            resolved_at DATETIME,
            resolution_strategy TEXT
        )""",
        
        "CREATE INDEX IF NOT EXISTS idx_change_queue_sync_status ON local_change_queue(sync_status)",
        "CREATE INDEX IF NOT EXISTS idx_conflicts_resolved ON sync_conflicts(resolved_at)"
    ]
    
    for sql in migration_sql:
        await self.db.execute(sql)
    
    await self.db.commit()
```

### 3. 数据兼容性处理

#### 字段兼容性映射
```python
class DataCompatibilityHandler:
    def __init__(self):
        self.field_mappings = {
            # 字段重命名映射
            'renames': {
                'old_field_name': 'new_field_name',
                'server_ip': 'asset_ip'  # v1.1.0中重命名
            },
            
            # 字段类型转换
            'type_conversions': {
                'port': {
                    'from': 'TEXT',
                    'to': 'INTEGER',
                    'converter': self.convert_port_to_int
                }
            },
            
            # 新增字段默认值
            'default_values': {
                'asset_type': 'server',
                'sync_version': 1,
                'is_deleted': 0
            },
            
            # 废弃字段处理
            'deprecated_fields': {
                'old_unused_field': {
                    'deprecated_in': '1.2.0',
                    'removed_in': '2.0.0',
                    'migration_strategy': 'ignore'
                }
            }
        }
    
    async def transform_data_for_compatibility(self, data, source_version, target_version):
        """为兼容性转换数据"""
        
        transformed_data = data.copy()
        
        # 1. 处理字段重命名
        for old_name, new_name in self.field_mappings['renames'].items():
            if old_name in transformed_data:
                transformed_data[new_name] = transformed_data.pop(old_name)
        
        # 2. 处理类型转换
        for field, conversion in self.field_mappings['type_conversions'].items():
            if field in transformed_data:
                converter = conversion['converter']
                try:
                    transformed_data[field] = await converter(transformed_data[field])
                except Exception as e:
                    await self.log_conversion_error(field, transformed_data[field], e)
        
        # 3. 添加缺失字段的默认值
        for field, default_value in self.field_mappings['default_values'].items():
            if field not in transformed_data:
                transformed_data[field] = default_value
        
        # 4. 移除废弃字段（如果目标版本不支持）
        deprecated_fields = self.get_deprecated_fields_for_version(target_version)
        for field in deprecated_fields:
            transformed_data.pop(field, None)
        
        return transformed_data

async def convert_port_to_int(self, port_value):
    """端口字段类型转换"""
    
    if isinstance(port_value, int):
        return port_value
    
    if isinstance(port_value, str):
        try:
            return int(port_value)
        except ValueError:
            # 如果无法转换，使用默认端口
            return 22
    
    return 22  # 默认SSH端口
```

#### 同步数据版本处理
```python
async def handle_cross_version_sync(self, local_data, remote_data, local_version, remote_version):
    """处理跨版本同步"""
    
    try:
        # 1. 检查版本兼容性
        compatibility = await self.check_version_compatibility(local_version, remote_version)
        
        if not compatibility['compatible']:
            return await self.handle_incompatible_sync(compatibility, local_data, remote_data)
        
        # 2. 转换数据格式
        if local_version != remote_version:
            # 将远程数据转换为本地版本格式
            compatible_remote_data = await self.transform_data_for_compatibility(
                remote_data, remote_version, local_version
            )
            
            # 将本地数据转换为远程版本格式（用于上传）
            compatible_local_data = await self.transform_data_for_compatibility(
                local_data, local_version, remote_version
            )
        else:
            compatible_remote_data = remote_data
            compatible_local_data = local_data
        
        # 3. 执行正常同步流程
        sync_result = await self.perform_compatible_sync(
            compatible_local_data, compatible_remote_data
        )
        
        return sync_result
        
    except Exception as e:
        await self.handle_cross_version_sync_error(e, local_version, remote_version)
        raise

async def handle_incompatible_sync(self, compatibility, local_data, remote_data):
    """处理不兼容的同步"""
    
    if compatibility['action'] == 'major_update_required':
        # 需要主要版本更新
        await self.show_major_update_dialog(compatibility)
        return {'success': False, 'reason': 'major_update_required'}
        
    elif compatibility['action'] == 'update_recommended':
        # 建议更新
        user_choice = await self.show_update_recommendation_dialog(compatibility)
        
        if user_choice == 'update_now':
            await self.trigger_app_update()
            return {'success': False, 'reason': 'updating'}
        elif user_choice == 'continue_limited':
            # 继续有限同步（只同步兼容的字段）
            return await self.perform_limited_sync(local_data, remote_data, compatibility)
        else:
            return {'success': False, 'reason': 'user_cancelled'}
```

### 4. 版本升级用户体验

#### 升级进度界面
```python
async def show_version_upgrade_dialog(self, old_version, new_version, upgrade_steps):
    """显示版本升级对话框"""
    
    upgrade_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                    应用版本升级                              │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  检测到应用版本升级：                                        │
    │  {old_version} → {new_version}                              │
    │                                                             │
    │  📋 升级内容：                                              │
    │  • 数据库结构优化                                            │
    │  • 新增同步功能                                              │
    │  • 性能改进                                                  │
    │  • 安全性增强                                                │
    │                                                             │
    │  ⏱️  预计耗时：{self.estimate_upgrade_time(upgrade_steps)} 分钟                        │
    │                                                             │
    │  ⚠️  升级期间请勿关闭应用                                    │
    │                                                             │
    │  进度: [░░░░░░░░░░░░░░░░░░░░] 0%                             │
    │  当前步骤: 准备升级...                                       │
    │                                                             │
    │  [ 开始升级 ]  [ 稍后提醒 ]  [ 了解详情 ]                  │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_upgrade_dialog(upgrade_ui)

async def show_upgrade_completion_dialog(self, upgrade_result):
    """显示升级完成对话框"""
    
    completion_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                    升级完成                                  │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  ✅ 应用已成功升级到 {upgrade_result['new_version']}                     │
    │                                                             │
    │  📊 升级统计：                                              │
    │  • 迁移记录数：{upgrade_result['migrated_records']} 条                      │
    │  • 升级耗时：{upgrade_result['duration']} 秒                         │
    │  • 数据完整性：✅ 验证通过                                   │
    │                                                             │
    │  🆕 新功能：                                                │
    │  • 改进的同步性能                                            │
    │  • 增强的冲突处理                                            │
    │  • 新的备份功能                                              │
    │                                                             │
    │  📱 同步状态：已自动恢复                                     │
    │                                                             │
    │  [ 开始使用 ]  [ 查看更新日志 ]                             │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_completion_dialog(completion_ui)
```

## 📊 方案优势

1. **平滑升级**: 自动检测版本并执行数据库迁移
2. **向前兼容**: 新版本能处理旧版本数据
3. **向后兼容**: 旧版本能忽略新版本字段
4. **安全回滚**: 升级失败时自动回滚
5. **用户友好**: 清晰的升级进度和说明
6. **灵活配置**: 支持复杂的版本兼容性规则

## 🎯 适用场景

- ✅ 应用版本更新
- ✅ 数据库结构变更
- ✅ API接口升级
- ✅ 功能迭代发布
- ✅ 跨版本兼容

这个方案确保了应用在版本升级过程中的数据同步兼容性和用户体验。
